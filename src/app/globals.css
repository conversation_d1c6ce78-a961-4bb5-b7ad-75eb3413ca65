/* Tailwind directives */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  --foreground-rgb: 255, 255, 255;
  --background-rgb: 4, 1, 40;
  --color-primary: #673de6;
  --color-text-secondary: #CCCCCC;
}

html {
  scroll-behavior: smooth;
}

/* Default body styles - keep global for landing page */
body {
  color: rgb(var(--foreground-rgb));
  /* Background color is now handled by theme */
}

/* Dashboard-specific theme overrides */
.dashboard-container.light-theme {
  color: #18181b; 
}

.dashboard-container.dark-theme {
  color: rgb(var(--foreground-rgb));
}

/* These classes are used throughout the codebase */
.font-title {
  font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
}

.font-body {
  font-family: var(--font-nunito), "<PERSON><PERSON><PERSON>", var(--font-zilla-slab), "Zilla Slab", sans-serif;
}

/* Khmer font classes */
.font-khmer-title {
  font-family: var(--font-khmer-title), "Dangrek", sans-serif;
}

.font-khmer-body {
  font-family: var(--font-khmer-body), "Noto Sans Khmer", sans-serif;
}

/* Apply Khmer fonts when the language is set to Khmer */
html[lang="kh"] .font-title {
  font-family: var(--font-khmer-title), "Dangrek", var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
}

html[lang="kh"] .font-body {
  font-family: var(--font-khmer-body), "Noto Sans Khmer", var(--font-nunito), "Nunito", var(--font-zilla-slab), "Zilla Slab", sans-serif;
}

/* Mobile-friendly hover states */
@media (hover: hover) and (pointer: fine) {
  /* Apply hover effects only on devices that support hover (desktops) */
  .hover\:border-white\/40:hover {
    border-color: rgba(255, 255, 255, 0.4);
  }
  
  .hover\:bg-jade-purple-dark:hover {
    background-color: var(--color-jade-purple-dark, #4b3a8a);
  }
}

/* Active state for touch devices */
.hover\:bg-jade-purple-dark:active {
  opacity: 0.8;
  transition: opacity 0.1s ease-out;
}

/* Reset tap highlight color on mobile */
a, button, [role="button"], [tabindex] {
  -webkit-tap-highlight-color: transparent;
}

.btn-primary {
  background-color:  rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(134, 107, 255, 0.4);
  box-shadow: 0 0 10px rgba(134, 107, 255, 0.4);
  padding: 0.5rem 1.5rem;
  border-radius: 1rem;
  font-weight: 500;
  transition: all 300ms;
  font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
}
.btn-primary:hover {
  background-color: rgba(134, 107, 255, 0.05);
  border: 1px solid rgba(134, 107, 255, 1);
}

.btn-secondary {
  background-color: transparent;
  border: 1px solid rgba(134, 107, 255, 0.4);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 1rem;
  font-weight: 500;
  transition: all 300ms;
  font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
}
.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(134, 107, 255, 1);
}

  /* Glassmorphism button with semi-transparent white background */
  .btn-glass-filled {
    background-color: rgba(134, 107, 255, 0.5);
    border: 2px solid rgba(134, 107, 255, 0.75);
    box-shadow: 0 0 15px rgba(134, 107, 255, 0.4);
    border-radius: 1rem;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 500;
    transition: all 300ms;
    font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
  }
  .btn-glass-filled:hover {
    box-shadow: 0 0 20px rgba(134, 107, 255, 0.5);
    border-color: rgba(134, 107, 255, 1);
    background-color: transparent;
    /* background-color: rgba(255, 255, 255, 0.1); */
    /* box-shadow: 0 0 25px rgba(134, 107, 255, 0.6);
    border-color: rgba(134, 107, 255, 0.8); */
  }

  /* Glassmorphism button with transparent center and glowing border */
  .btn-glass-outline {
    background-color: transparent;
    border: 2px solid rgba(134, 107, 255, 0.4);
    box-shadow: 0 0 15px rgba(134, 107, 255, 0.4);
    border-radius: 1rem;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 500;
    transition: all 300ms;
    font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
  }
  .btn-glass-outline:hover {
    box-shadow: 0 0 20px rgba(134, 107, 255, 0.5);
    border-color: rgba(134, 107, 255, 1);
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Glassmorphism button with semi-transparent white background (no shadow) */
  .btn-glass-filled-flat {
    background-color: rgba(134, 107, 255, 0.5);
    border: 2px solid rgba(134, 107, 255, 0.75);
    border-radius: 1rem;
    padding: 0.25rem 1rem;
    color: white;
    font-weight: 400;
    transition: all 300ms;
    font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
  }
  .btn-glass-filled-flat:hover {
    border-color: rgba(134, 107, 255, 1);
    background-color: transparent;
    /* border-color: rgba(134, 107, 255, 0.8); */
  }

  /* Glassmorphism button with transparent center (no shadow) */
  .btn-glass-outline-flat {
    background-color: transparent;
    border: 2px solid rgba(134, 107, 255, 0.4);
    border-radius: 1rem;
    padding: 0.25rem 1rem;
    color: white;
    font-weight: 400;
    transition: all 300ms;
    font-family: var(--font-raleway), "Raleway", var(--font-caladea), "Caladea", sans-serif;
  }
  .btn-glass-outline-flat:hover {
    border-color: rgba(134, 107, 255, 1);
    background-color: rgba(255, 255, 255, 0.1);
  }

  .gradient-text {
    color: #866bff;
  }

  .card {
    background-color: #040128;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
    border: 1px solid rgb(39, 39, 42);
    color: white;
  }
  .card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .terminal {
    background-color: #040128;
    border-radius: 0.5rem;
    padding: 1rem;
    color: #FFFFFF;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgb(39, 39, 42);
  }

  .section {
    padding: 3rem 1rem;
    max-width: 80rem;
    margin-left: auto;
    margin-right: auto;
  }
  @media (min-width: 768px) {
    .section {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Mobile backdrop filter fix */
  @media (max-width: 767px) {
    .platform-card {
      background-color: rgba(3, 1, 31, 0.95) !important; /* deep-blue with higher opacity */
      /* backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important; */
    }

    /* Terminal animation mobile fixes */
    .terminal-container {
      background-color: rgba(3, 1, 31, 0.95) !important; /* deep-blue with higher opacity */
      /* backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important; */
    }

    /* .terminal-bot-message {
      background-color: rgba(134, 107, 255, 0.8) !important; /* jade-purple with higher opacity */
      /* backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
    } */

    /* .terminal-user-message {
      background-color: rgba(255, 255, 255, 0.25) !important; /* white with higher opacity */
      /* backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
    /* } */

    /* .terminal-header, .terminal-footer {
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
    } */

    .terminal-date-badge {
      background-color: rgba(255, 255, 255, 0.25) !important;
      /* backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important; */
    }

    /* .terminal-input-field {
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
    } */
  }
