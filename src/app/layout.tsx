import './globals.css'
import type { Metadata } from 'next'
import { fontVariables } from '@/styles/fonts'
import { AuthProvider } from '@/context/AuthContext'
import { LanguageProvider } from '@/context/LanguageContext'
import { LoadingProvider } from '@/context/LoadingContext'
import { ThemeProvider } from '@/context/ThemeContext'

export const metadata: Metadata = {
  title: 'ChhlatBot - Intelligent Conversations for Social Media',
  description: 'Automate customer engagement across all your social platforms with ChhlatBot.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="kh" className={`scroll-smooth ${fontVariables}`}>
      <head>
        {/* Cloudflare Web Analytics will be automatically injected via dashboard */}
      </head>
      <body className="antialiased">
        <AuthProvider>
          <LanguageProvider>
            <ThemeProvider>
              <LoadingProvider>
                {children}
              </LoadingProvider>
            </ThemeProvider>
          </LanguageProvider>
        </AuthProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Initialize theme class on body to prevent flash of unstyled content
              (function() {
                try {
                  const savedTheme = localStorage.getItem('uiTheme') || 'dark';
                  document.body.className = document.body.className.replace(/\\b(light-theme|dark-theme)\\b/g, '');
                  document.body.classList.add(savedTheme + '-theme');
                } catch (e) {
                  console.error('Error initializing theme:', e);
                }
              })();
            `,
          }}
        />
      </body>
    </html>
  )
}