'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import NextImage from 'next/image'
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { FaBrain, FaListAlt, FaImage, FaTrash, FaExclamationTriangle, FaCheck, FaTimes } from 'react-icons/fa';
import { Button } from '@/components/ui/Button';
import { LinkButton } from '@/components/ui/LinkButton';

import { useDashboardData, updatePhotoCountInCache, usePhotosData, getPhotosFromCache, updatePhotosInCache } from '@/hooks/useOptimizedData'
import imageCompression from 'browser-image-compression'
import { v4 as uuidv4 } from 'uuid'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonElement} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 ${themeConfig.skeletonElement} transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

// Photo type definition
type Photo = {
  id: string;
  photo_id: string;
  photo_url: string;
  photo_file_path: string;
}

export default function PhotoPage() {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const supabase = createClientComponentClient()

  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  // Use optimized photos data hook with cache - no refetch interval
  const { data: photosData, loading: photosLoading, refetch: refetchPhotos } = usePhotosData()

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const photoUsagePercentage = knowledgeStats?.photoUsagePercentage || 0
  const isLoadingCount = isDashboardLoading

  // Legacy stats state (keeping for compatibility)
  const [businessInsightCount, setBusinessInsightCount] = useState(0)
  const [productListCount, setProductListCount] = useState(0)
  const [productListLimit, setProductListLimit] = useState<number>(1)

  // Photo catalog state
  const [photos, setPhotos] = useState<Array<any>>([])
  const [filteredPhotos, setFilteredPhotos] = useState<Array<any>>([])
  const [isLoadingPhotos, setIsLoadingPhotos] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 25

  // Photo upload state
  const [isUploading, setIsUploading] = useState(false)
  const [photoId, setPhotoId] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [showUploadForm, setShowUploadForm] = useState(false)
  const [uploadError, setUploadError] = useState('')
  const [isProcessingImage, setIsProcessingImage] = useState(false)
  const [imagePreviews, setImagePreviews] = useState<Array<{id: string, url: string}>>([])
  const dropAreaRef = useRef<HTMLDivElement>(null)

  // Photo ID validation state
  const [isCheckingPhotoId, setIsCheckingPhotoId] = useState(false)
  const [photoIdExists, setPhotoIdExists] = useState(false)
  const [photoIdValidationMessage, setPhotoIdValidationMessage] = useState('')
  const [photoIdCheckTimeout, setPhotoIdCheckTimeout] = useState<NodeJS.Timeout | null>(null)

  // Photo update state
  const [showUpdateForm, setShowUpdateForm] = useState(false)
  const [photoToUpdate, setPhotoToUpdate] = useState<any>(null)
  const [updatePhotoId, setUpdatePhotoId] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateError, setUpdateError] = useState('')
  const [updateSelectedFiles, setUpdateSelectedFiles] = useState<File[]>([])
  const [updateImagePreviews, setUpdateImagePreviews] = useState<Array<{id: string, url: string, isExisting: boolean, url_index?: number}>>([])
  const updateDropAreaRef = useRef<HTMLDivElement>(null)

  // Confirmation modals
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)
  const [showUploadConfirm, setShowUploadConfirm] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showUpdateConfirm, setShowUpdateConfirm] = useState(false)
  const [showCancelUpdateConfirm, setShowCancelUpdateConfirm] = useState(false)
  const [photoToDelete, setPhotoToDelete] = useState<any>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteSuccess, setDeleteSuccess] = useState(false)

  // Photo removal confirmation
  const [showRemovePhotoConfirm, setShowRemovePhotoConfirm] = useState(false)
  const [photoIndexToRemove, setPhotoIndexToRemove] = useState<number | null>(null)
  const [isRemovingFromUpdate, setIsRemovingFromUpdate] = useState(false)

  // Cannot delete photo modal (when photo is linked to FAQs)
  const [showCannotDeleteModal, setShowCannotDeleteModal] = useState(false)
  const [linkedQuestions, setLinkedQuestions] = useState<string[]>([])
  const cannotDeleteModalRef = useRef<HTMLDivElement>(null)

  // Refs for modals
  const cancelConfirmRef = useRef<HTMLDivElement>(null)
  const uploadConfirmRef = useRef<HTMLDivElement>(null)
  const deleteConfirmRef = useRef<HTMLDivElement>(null)
  const updateConfirmRef = useRef<HTMLDivElement>(null)
  const cancelUpdateConfirmRef = useRef<HTMLDivElement>(null)
  const removePhotoConfirmRef = useRef<HTMLDivElement>(null)

  // Status overlay for upload progress
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)
  const statusOverlayRef = useRef<HTMLDivElement>(null)

  // Image preview state
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [previewImages, setPreviewImages] = useState<string[]>([])
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Memoize filtered photos calculation
  const filterPhotos = useCallback((data: any[], query: string) => {
    if (!query.trim()) return data;
    return data.filter((photo: any) =>
      photo.photo_id.toLowerCase().includes(query.toLowerCase())
    );
  }, []);

  // Update photos when photosData changes
  useEffect(() => {
    let mounted = true;

    const updatePhotoState = () => {
      if (!mounted || !photosData) return;

      setIsLoadingPhotos(true);
      
      // Update base photos
      setPhotos(photosData);
      
      // Calculate filtered photos
      const filtered = filterPhotos(photosData, searchQuery);
      setFilteredPhotos(filtered);
      updateTotalPages(filtered.length);

      // Reset to first page when data changes
      setCurrentPage(1);
      setIsLoadingPhotos(false);
    };

    updatePhotoState();

    return () => {
      mounted = false;
    };
  }, [photosData, searchQuery, filterPhotos]); // Add filterPhotos to dependencies

  // Handle search input change with debounce
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  }, []);

  // Get current page items - memoize to prevent unnecessary recalculations
  const getCurrentPageItems = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredPhotos.slice(startIndex, endIndex);
  }, [currentPage, filteredPhotos, itemsPerPage]);

  // Check if photo ID exists in local photos array
  const checkPhotoIdExists = useCallback((id: string): boolean => {
    if (!id.trim()) {
      return false;
    }

    // Capitalize the ID before checking since all IDs in database are capitalized
    const capitalizedId = id.trim().toUpperCase();

    // Check against local photos array
    return photos.some(photo => photo.photo_id === capitalizedId);
  }, [photos]);

  // Debounced photo ID validation
  const validatePhotoId = useCallback((id: string) => {
    // Clear any existing timeout
    if (photoIdCheckTimeout) {
      clearTimeout(photoIdCheckTimeout);
    }

    // Reset validation state
    setPhotoIdValidationMessage('');
    setPhotoIdExists(false);

    if (!id.trim()) {
      return;
    }

    // Set loading state
    setIsCheckingPhotoId(true);

    // Set a new timeout for debounced checking
    const timeoutId = setTimeout(() => {
      try {
        const exists = checkPhotoIdExists(id);
        setPhotoIdExists(exists);

        if (exists) {
          setPhotoIdValidationMessage(t('photo_id_exists') || 'Photo ID already exists');
        } else {
          setPhotoIdValidationMessage('');
        }
      } catch (error) {
        console.error('Error validating photo ID:', error);
        setPhotoIdValidationMessage('');
      } finally {
        setIsCheckingPhotoId(false);
      }
    }, 500); // 500ms debounce

    setPhotoIdCheckTimeout(timeoutId);
  }, [photoIdCheckTimeout, checkPhotoIdExists, t]);


  // Validate file
  const validateFile = (file: File): string | null => {
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return t('file_size_error').replace('{size}', (file.size / (1024 * 1024)).toFixed(2));
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      return t('file_type_error');
    }

    return null;
  };

  // Handle image compression and processing
  const processImage = async (file: File): Promise<{ compressedFile: File }> => {
    // Compression options with consistent settings (same as productList)
    const options = {
      maxSizeMB: 0.7,               // Limit file size to 700KB
      maxWidthOrHeight: 1080,       // Resize to max 1080 width/height
      useWebWorker: true,           // Use web worker for better performance
      fileType: 'image/jpeg',       // Always convert to JPEG
      initialQuality: 0.8,          // High quality (80%)
      alwaysKeepResolution: true    // Maintain aspect ratio
    };

    try {

      // Compress the image
      const compressedBlob = await imageCompression(file, options);

      // Create a consistent filename with timestamp to avoid collisions
      const timestamp = Date.now();
      const baseFileName = file.name.split('.')[0].replace(/[^a-zA-Z0-9]/g, '_');
      const fileName = `${baseFileName}_${timestamp}.jpg`;

      // Create a proper File object from the blob
      const compressedFile = new File([compressedBlob], fileName, {
        type: 'image/jpeg',
        lastModified: Date.now()
      });

      return { compressedFile };

    } catch (error) {
      console.error('Image compression failed:', error);
      throw error;
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUploadError('');

    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      const currentCount = selectedFiles.length;
      const remainingSlots = 4 - currentCount;

      if (files.length > remainingSlots) {
        setUploadError(t('remaining_slots_error').replace('{count}', remainingSlots.toString()));
        // Process only the allowed number of files
        files.slice(0, remainingSlots).forEach(file => handleImageFile(file));
      } else {
        files.forEach(file => handleImageFile(file));
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  // Handle single image file
  const handleImageFile = async (file: File) => {
    // Validate file
    const error = validateFile(file);
    if (error) {
      setUploadError(error);
      return;
    }

    try {
      setIsProcessingImage(true);

      // Step 1: Compress the image
      const { compressedFile } = await processImage(file);

      // Step 2: Create a preview from the compressed file
      const previewUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // Step 3: Update state with the compressed file
      setSelectedFiles(prev => [...prev, compressedFile]);
      setImagePreviews(prev => [...prev, {
        id: `preview-${uuidv4()}`,
        url: previewUrl
      }]);

    } catch (error) {
      console.error('Error processing image:', error);
      setUploadError(t('process_error'));
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Show confirmation before removing a file
  const showRemoveFileConfirm = (index: number, isUpdate: boolean = false) => {
    setPhotoIndexToRemove(index);
    setIsRemovingFromUpdate(isUpdate);
    setShowRemovePhotoConfirm(true);
  };

  // Confirm and remove a file from the selection
  const confirmRemoveFile = () => {
    if (photoIndexToRemove === null) return;

    if (isRemovingFromUpdate) {
      // Remove from update form
      const preview = updateImagePreviews[photoIndexToRemove];

      if (preview.isExisting) {
        // Remove existing image
        setUpdateImagePreviews(prev => prev.filter((_, i) => i !== photoIndexToRemove));
      } else {
        // Find the corresponding new file index
        const newFileIndex = updateImagePreviews.filter(p => p.isExisting).length;
        const fileIndexToRemove = photoIndexToRemove - newFileIndex;

        // Remove new image
        setUpdateSelectedFiles(prev => prev.filter((_, i) => i !== fileIndexToRemove));
        setUpdateImagePreviews(prev => prev.filter((_, i) => i !== photoIndexToRemove));
      }
    } else {
      // Remove from upload form
      setSelectedFiles(prev => prev.filter((_, i) => i !== photoIndexToRemove));
      setImagePreviews(prev => prev.filter((_, i) => i !== photoIndexToRemove));
    }

    // Reset state
    setShowRemovePhotoConfirm(false);
    setPhotoIndexToRemove(null);
    setIsRemovingFromUpdate(false);
  };

  // Cancel remove file
  const cancelRemoveFile = () => {
    setShowRemovePhotoConfirm(false);
    setPhotoIndexToRemove(null);
    setIsRemovingFromUpdate(false);
  };

  // Legacy remove file function (now just calls the confirmation)
  const removeFile = (index: number) => {
    showRemoveFileConfirm(index, false);
  };

  // Handle photo ID input with real-time validation
  const handlePhotoIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPhotoId(value);

    // Clear upload error when user starts typing
    if (uploadError) {
      setUploadError('');
    }

    // Trigger real-time validation
    validatePhotoId(value);
  };

  // Handle photo ID blur (for capitalization only)
  const handlePhotoIdBlur = () => {
    if (photoId.trim()) {
      const capitalizedId = photoId.trim().toUpperCase();
      // Only update the input field if capitalization changed it
      if (capitalizedId !== photoId) {
        setPhotoId(capitalizedId);
        // No re-validation needed - real-time validation already handled it
      }
    }
  };

  // Handle form submission - Show confirmation first
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedFiles.length === 0 || !photoId.trim()) {
      setUploadError(t('select_file_id_error'));
      return;
    }

    // Check if photo ID already exists
    if (photoIdExists) {
      setUploadError(t('photo_id_exists') || 'Photo ID already exists');
      return;
    }

    // Double-check photo ID existence before proceeding
    const exists = checkPhotoIdExists(photoId.trim());
    if (exists) {
      setPhotoIdExists(true);
      setPhotoIdValidationMessage(t('photo_id_exists') || 'Photo ID already exists');
      setUploadError(t('photo_id_exists') || 'Photo ID already exists');
      return;
    }

    // Check if we've reached the photo limit
    // Note: We create 1 photo record with multiple files, not multiple records
    if (photoCount + 1 > photoLimit) {
      setUploadError(t('photo_limit_error').replace('{count}', (photoLimit - photoCount).toString()));
      return;
    }

    // Show confirmation dialog
    setShowUploadConfirm(true);
  };

  // Actual upload after confirmation
  const handleConfirmedUpload = async () => {
    setShowUploadConfirm(false);
    setIsUploading(true);
    setUpdateStatus('loading');
    setUpdateMessage(t('preparing_upload'));
    setUpdateProgress(10);

    try {

      setIsProcessingImage(true);

      // Get the user session for auth ID
      const { data: sessionData } = await supabase.auth.getSession();
      const userId = sessionData?.session?.user?.id;

      if (!userId) {
        throw new Error(t('auth_error'));
      }

      // Arrays to store URLs and file paths
      let photo_urls: string[] = [];
      let photo_file_paths: string[] = [];

      setUpdateMessage(t('uploading_photos'));
      setUpdateProgress(20);

      // Upload each file to storage and save to database
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];

        // Generate a unique filename using UUID (same as productList)
        const uniqueId = uuidv4();
        const uniqueFileName = `${photoId}-${uniqueId}.jpg`; // Always jpg after compression
        const filePath = `${userId}/${uniqueFileName}`;

        // Update progress for each file
        setUpdateMessage(t('uploading_photo').replace('{current}', (i+1).toString()).replace('{total}', selectedFiles.length.toString()));

        // Upload file to 'photos' bucket
        const { error: uploadError } = await supabase.storage
          .from('photos')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true
          });

        if (uploadError) {
          throw new Error(t('upload_error').replace('{count}', (i+1).toString()).replace('{message}', uploadError.message));
        }

        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('photos')
          .getPublicUrl(filePath);

        if (!urlData?.publicUrl) {
          throw new Error(t('url_error'));
        }

        // Store the URL and path
        photo_urls.push(urlData.publicUrl);
        photo_file_paths.push(filePath);

        // Update progress based on file upload completion
        setUpdateProgress(20 + Math.floor((i + 1) * 60 / selectedFiles.length));
      }

      setUpdateMessage(t('saving_photo_info'));
      setUpdateProgress(80);

      // Save photo via API
      const insertResponse = await fetch('/api/knowledge/photos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo_id: photoId,
          photo_url: photo_urls,
          photo_file_path: photo_file_paths
        })
      });

      const insertResult = await insertResponse.json();

      if (!insertResponse.ok) {
        throw new Error(insertResult.error || t('save_error').replace('{message}', 'Failed to save photo'));
      }

      setUpdateProgress(100);

      // Update session storage cache with new photo
      const newPhoto = {
        id: insertResult.id,
        photo_id: photoId,
        photo_url: photo_urls,
        photo_file_path: photo_file_paths,
        updated_at: new Date().toISOString()
      };
      const cached = getPhotosFromCache();
      const updatedPhotos = cached ? [newPhoto, ...cached.data] : [newPhoto];
      updatePhotosInCache(updatedPhotos);

      // Reset form
      setPhotoId('');
      setSelectedFiles([]);
      setImagePreviews([]);
      setShowUploadForm(false);

      // Update photo count in dashboard cache immediately
      // Note: We create 1 photo record with multiple URLs, not multiple records
      const newPhotoCount = photoCount + 1;
      updatePhotoCountInCache(newPhotoCount);

      // Show success message
      setUpdateStatus('success');
      setUpdateMessage(t('photos_uploaded'));

      // Auto-dismiss after delay
      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUploading(false);
      }, 1500);

    } catch (error) {
      console.error('Error uploading photos:', error);
      setUpdateStatus('error');
      setUpdateMessage(`Error uploading photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsUploading(false);
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Handle cancel button click
  const handleCancel = () => {
    // If there are photos, show confirmation dialog
    if (selectedFiles.length > 0) {
      setShowCancelConfirm(true);
    } else {
      // Otherwise just close the form
      closeUploadForm();
    }
  };

  // Confirm cancel and close form
  const confirmCancel = () => {
    setShowCancelConfirm(false);
    closeUploadForm();

    // If we have a photo to update (from edit button click), proceed with edit operation
    if (photoToUpdate) {
      const photo = photoToUpdate;

      // Reset photoToUpdate to avoid infinite loop
      setPhotoToUpdate(null);

      // Call setupEditForm to continue with the edit operation
      setTimeout(() => {
        setupEditForm(photo);
      }, 100);
    }
  };

  // Close upload form and reset state
  const closeUploadForm = () => {
    setShowUploadForm(false);
    setPhotoId('');
    setSelectedFiles([]);
    setImagePreviews([]);
    setUploadError('');

    // Reset photo ID validation state
    setIsCheckingPhotoId(false);
    setPhotoIdExists(false);
    setPhotoIdValidationMessage('');

    // Clear any pending timeout
    if (photoIdCheckTimeout) {
      clearTimeout(photoIdCheckTimeout);
      setPhotoIdCheckTimeout(null);
    }
  };

  // Toggle upload form
  const toggleUploadForm = () => {
    // If update form is open, check for changes
    if (showUpdateForm) {
      if (hasUpdateFormChanges()) {
        // Store a flag to indicate we want to show the upload form after discarding changes
        sessionStorage.setItem('showUploadFormAfterDiscard', 'true');
        setShowCancelUpdateConfirm(true);
      } else {
        closeUpdateForm();
        setShowUploadForm(true);
      }
      return;
    }

    // Handle upload form toggle
    if (showUploadForm) {
      // If form is open, handle cancel logic
      handleCancel();
    } else {
      // If form is closed, just open it
      setShowUploadForm(true);

      // Reset photoToUpdate to avoid confusion when switching from edit to add mode
      setPhotoToUpdate(null);
    }
  };





  // Disable page scroll when gallery is open
  useEffect(() => {
    if (previewImage) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [previewImage]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = showCancelConfirm || showUploadConfirm || showDeleteConfirm ||
                       showUpdateConfirm || showCancelUpdateConfirm || showRemovePhotoConfirm ||
                       showCannotDeleteModal || updateStatus === 'loading' || updateStatus === 'success' ||
                       updateStatus === 'error';

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [showCancelConfirm, showUploadConfirm, showDeleteConfirm, showUpdateConfirm,
      showCancelUpdateConfirm, showRemovePhotoConfirm, showCannotDeleteModal, updateStatus]);

  // Enable drag and drop for image upload
  useEffect(() => {
    const dropArea = dropAreaRef.current;
    if (!dropArea) return;

    const highlight = () => dropArea.classList.add('border-jade-purple');
    const unhighlight = () => dropArea.classList.remove('border-jade-purple');

    const preventDefaults = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = async (e: DragEvent) => {
      unhighlight();
      preventDefaults(e);

      if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files);
        const currentCount = selectedFiles.length;
        const remainingSlots = 4 - currentCount;

        if (files.length > remainingSlots) {
          setUploadError(`You can only add ${remainingSlots} more image(s).`);
          // Process only the allowed number of files
          files.slice(0, remainingSlots).forEach(file => handleImageFile(file));
        } else {
          files.forEach(file => handleImageFile(file));
        }
      }
    };

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName, index) => {
      dropArea.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach((eventName, index) => {
      dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach((eventName, index) => {
      dropArea.addEventListener(eventName, unhighlight, false);
    });

    dropArea.addEventListener('drop', handleDrop, false);

    return () => {
      if (dropArea) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName, index) => {
          dropArea.removeEventListener(eventName, preventDefaults);
        });

        ['dragenter', 'dragover'].forEach((eventName, index) => {
          dropArea.removeEventListener(eventName, highlight);
        });

        ['dragleave', 'drop'].forEach((eventName, index) => {
          dropArea.removeEventListener(eventName, unhighlight);
        });

        dropArea.removeEventListener('drop', handleDrop);
      }
    };
  }, []);

  // Fetch photos from API
  // const fetchPhotos = async () => {
  //   try {
  //     setIsLoadingPhotos(true);

  //     // Wait for dashboard data to load before fetching photos
  //     if (!clientInfo) {
  //       return
  //     }

  //     // Call photos API
  //     const response = await fetch('/api/knowledge/photos')
  //     const responseData = await response.json()

  //     if (!response.ok) {
  //       throw new Error(responseData.error || 'Failed to fetch photos')
  //     }

  //     const allPhotos = responseData.photos || [];
  //     setPhotos(allPhotos);

  //     // Apply search filter if there's a search query
  //     if (searchQuery.trim()) {
  //       const filtered = allPhotos.filter((photo: any) =>
  //         photo.photo_id.toLowerCase().includes(searchQuery.toLowerCase())
  //       );
  //       setFilteredPhotos(filtered);
  //       updateTotalPages(filtered.length);
  //     } else {
  //       setFilteredPhotos(allPhotos);
  //       updateTotalPages(allPhotos.length);
  //     }

  //     // Reset to first page when data changes
  //     setCurrentPage(1);
  //   } catch (error) {
  //     console.error('Error fetching photos:', error);
  //   } finally {
  //     setIsLoadingPhotos(false);
  //   }
  // };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching

    let filtered;
    if (!query.trim()) {
      filtered = photos;
      setFilteredPhotos(photos);
    } else {
      filtered = photos.filter(photo =>
        photo.photo_id.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredPhotos(filtered);
    }

    // Update total pages based on filtered results
    updateTotalPages(filtered.length);
  };

  // Update total pages based on item count
  const updateTotalPages = useCallback((itemCount: number) => {
    setTotalPages(Math.ceil(itemCount / itemsPerPage));
  }, [itemsPerPage]);

  // Handle pagination
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const handlePageClick = (page: number) => {
    setCurrentPage(page)
  }

  // Generate pagination buttons
  const renderPaginationButtons = () => {
    const buttons = [];
    const maxVisibleButtons = 5;

    let startPage, endPage;

    if (totalPages <= maxVisibleButtons) {
      startPage = 1;
      endPage = totalPages;
    } else {
      const halfWay = Math.floor(maxVisibleButtons / 2);

      if (currentPage <= halfWay + 1) {
        startPage = 1;
        endPage = maxVisibleButtons;
      } else if (currentPage >= totalPages - halfWay) {
        startPage = totalPages - maxVisibleButtons + 1;
        endPage = totalPages;
      } else {
        startPage = currentPage - halfWay;
        endPage = currentPage + halfWay;
      }
    }

    // Add first page button if not visible
    if (startPage > 1) {
      buttons.push(
        <div key="first-button-container" className="flex items-center">
          <Button
            onClick={() => handlePageClick(1)}
            variant="secondary"
            size="sm"
            className="min-w-[32px] h-7 text-xs"
          >
            1
          </Button>
          {startPage > 2 && <span className="px-2 text-zinc-400 text-xs">...</span>}
        </div>
      );
    }

    // Add page buttons
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <div key={`page-${i}-container`}>
          <Button
            onClick={() => handlePageClick(i)}
            variant={currentPage === i ? 'primary' : 'secondary'}
            size="sm"
            className={`min-w-[32px] h-7 text-xs ${
              currentPage === i ? 'bg-jade-purple' : ''
            }`}
          >
            {i}
          </Button>
        </div>
      );
    }

    // Add last page button if not visible
    if (endPage < totalPages) {
      buttons.push(
        <div key="last-button-container" className="flex items-center">
          {endPage < totalPages - 1 && <span className="px-2 text-zinc-400 text-xs">...</span>}
          <Button
            onClick={() => handlePageClick(totalPages)}
            variant="secondary"
            size="sm"
            className="min-w-[32px] h-7 text-xs"
          >
            {totalPages}
          </Button>
        </div>
      );
    }

    return buttons;
  };

  // Handle delete photo
  const handleDeleteClick = (photo: any) => {
    setPhotoToDelete(photo);
    setShowDeleteConfirm(true);
    setIsDeleting(false);
    setDeleteSuccess(false);
  };

  // Confirm delete photo
  const confirmDeletePhoto = async () => {
    if (!photoToDelete) return;

    setIsDeleting(true);

    try {

      const userLang = clientInfo?.lang || 'en';
      // 1. Check if the photo is linked to any FAQs via API
      const dependencyResponse = await fetch(`/api/knowledge/photos/check-dependencies?photo_id=${photoToDelete.photo_id}&lang=${userLang}`);
      const dependencyData = await dependencyResponse.json();

      if (!dependencyResponse.ok) {
        console.error('Error checking linked FAQs:', dependencyData.error);
        throw new Error(`Error checking linked FAQs: ${dependencyData.error}`);
      }

      // If there are linked FAQs, show the cannot delete modal
      if (dependencyData.hasLinkedFaqs) {
        setLinkedQuestions(dependencyData.linkedQuestions);
        setShowCannotDeleteModal(true);
        setShowDeleteConfirm(false);
        setIsDeleting(false);
        return;
      }

      // 2. Delete files from storage bucket
      if (Array.isArray(photoToDelete.photo_file_path)) {
        // Delete multiple files
        for (const filePath of photoToDelete.photo_file_path) {
          const { error } = await supabase.storage
            .from('photos')
            .remove([filePath]);

          if (error) {
            console.error(`Error deleting file ${filePath}:`, error);
          }
        }
      } else if (photoToDelete.photo_file_path) {
        // Delete single file
        const { error } = await supabase.storage
          .from('photos')
          .remove([photoToDelete.photo_file_path]);

        if (error) {
          console.error('Error deleting file:', error);
        }
      }

      // 3. Delete record from photos table via API
      const deleteResponse = await fetch(`/api/knowledge/photos?photo_id=${photoToDelete.photo_id}`, {
        method: 'DELETE'
      });

      const deleteResult = await deleteResponse.json();

      if (!deleteResponse.ok) {
        throw new Error(`Error deleting photo record: ${deleteResult.error}`);
      }

      // Show success message before closing
      setIsDeleting(false);
      setDeleteSuccess(true);

      // Update session storage cache
      const cached = getPhotosFromCache();
      if (cached) {
        const updatedPhotos = cached.data.filter(p => p.id !== photoToDelete.id);
        updatePhotosInCache(updatedPhotos);
      }

      // Wait a moment to show success message, then close modal
      setTimeout(() => {
        // Close modal
        setShowDeleteConfirm(false);

        // Update photo count in dashboard cache immediately
        const newPhotoCount = Math.max(photoCount - 1, 0);
        updatePhotoCountInCache(newPhotoCount);

        // Reset state
        setPhotoToDelete(null);
        setDeleteSuccess(false);
      }, 1000);

    } catch (error) {
      console.error('Error deleting photo:', error);
      alert(`Error deleting photo: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsDeleting(false);
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setPhotoToDelete(null);
  };

  // Function to set up the edit form with photo data
  const setupEditForm = (photo: any) => {
    setPhotoToUpdate(photo);
    setUpdatePhotoId(photo.photo_id);
    setShowUpdateForm(true);
    setUpdateError('');
    setIsUpdating(false);
    setUpdateSelectedFiles([]);

    // Initialize image previews from existing photos
    const existingUrls = Array.isArray(photo.photo_url) ? photo.photo_url : [photo.photo_url];
    const previews = existingUrls.map((url: string, index: number) => ({
      id: `existing-${index}`,
      url: url,
      isExisting: true,
      url_index: index
    }));

    setUpdateImagePreviews(previews);

    // Hide the upload form if it's open
    setShowUploadForm(false);

    // Smooth scroll to the update form
    setTimeout(() => {
      const updateForm = document.querySelector('.update-form-container');
      if (updateForm) {
        updateForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // Handle edit button click
  const handleEditClick = (photo: any) => {
    // Check if upload form is open and has changes
    if (showUploadForm && selectedFiles.length > 0) {
      // Show confirmation dialog
      setShowCancelConfirm(true);

      // Store the photo to update for after confirmation
      setPhotoToUpdate(photo);
      return;
    }

    // Proceed with edit operation
    setupEditForm(photo);
  };

  // Photo ID is now read-only, so we don't need a change handler

  // Handle file selection for update
  const handleUpdateFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUpdateError('');

    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      const currentCount = updateSelectedFiles.length + updateImagePreviews.filter(p => p.isExisting).length;
      const remainingSlots = 4 - currentCount;

      if (files.length > remainingSlots) {
        setUpdateError(t('remaining_slots_error').replace('{count}', remainingSlots.toString()));
        // Process only the allowed number of files
        files.slice(0, remainingSlots).forEach(file => handleUpdateImageFile(file));
      } else {
        files.forEach(file => handleUpdateImageFile(file));
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  // Handle single image file for update
  const handleUpdateImageFile = async (file: File) => {
    // Validate file
    const error = validateFile(file);
    if (error) {
      setUpdateError(error);
      return;
    }

    try {
      setIsProcessingImage(true);

      // Compress the image
      const { compressedFile } = await processImage(file);

      // Create a preview from the compressed file
      const previewUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // Update state with the compressed file
      setUpdateSelectedFiles(prev => [...prev, compressedFile]);
      setUpdateImagePreviews(prev => [...prev, {
        id: `new-${uuidv4()}`,
        url: previewUrl,
        isExisting: false
      }]);

    } catch (error) {
      console.error('Error processing image:', error);
      setUpdateError(t('process_error'));
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Remove a file from the update selection (now just calls the confirmation)
  const removeUpdateFile = (index: number) => {
    showRemoveFileConfirm(index, true);
  };

  // Check if update form has changes
  const hasUpdateFormChanges = () => {
    if (!photoToUpdate) return false;

    // Check if photos were added or removed
    const originalPhotoCount = Array.isArray(photoToUpdate.photo_url)
      ? photoToUpdate.photo_url.length
      : (photoToUpdate.photo_url ? 1 : 0);

    if (updateImagePreviews.length !== originalPhotoCount) return true;

    // Check if any new photos were added
    if (updateSelectedFiles.length > 0) return true;

    // If we got here, nothing changed
    return false;
  };

  // Show cancel update confirmation
  const showCancelUpdateConfirmation = () => {
    if (hasUpdateFormChanges()) {
      setShowCancelUpdateConfirm(true);
    } else {
      // No changes, just close the form
      closeUpdateForm();
    }
  };

  // Close update form without confirmation
  const closeUpdateForm = () => {
    setShowUpdateForm(false);
    setPhotoToUpdate(null);
    setUpdatePhotoId('');
    setUpdateSelectedFiles([]);
    setUpdateImagePreviews([]);
    setUpdateError('');
  };

  // Cancel update
  const handleCancelUpdate = () => {
    showCancelUpdateConfirmation();
  };

  // Show update confirmation
  const showUpdateConfirmation = (e: React.FormEvent) => {
    e.preventDefault();

    if (!updatePhotoId.trim()) {
      setUpdateError(t('select_file_id_error'));
      return;
    }

    if (updateImagePreviews.length === 0) {
      setUpdateError(t('select_file_id_error'));
      return;
    }

    setShowUpdateConfirm(true);
  };

  // Delete photos from storage
  const deletePhotosFromStorage = async (filePaths: string[]) => {
    try {
      const { error } = await supabase.storage
        .from('photos')
        .remove(filePaths);

      if (error) {
        console.error('Error deleting old photos:', error);
      }
    } catch (error) {
      console.error('Error in deletePhotosFromStorage:', error);
    }
  };

  // Handle update form submission
  const handleUpdateSubmit = async () => {
    setShowUpdateConfirm(false);
    setIsUpdating(true);

    // Show progress bar
    setUpdateStatus('loading');
    setUpdateMessage('Preparing to update photo...');
    setUpdateProgress(10);

    try {
      // Get the user session for auth ID
      const { data: sessionData } = await supabase.auth.getSession();
      const userId = sessionData?.session?.user?.id;

      if (!userId) {
        throw new Error(t('auth_error'));
      }

      // Arrays to store URLs and file paths
      let photo_urls: string[] = [];
      let photo_file_paths: string[] = [];

      // Track which files to delete
      const filesToDelete = [...photoToUpdate.photo_file_path];

      // 1. Keep existing files that weren't removed
      setUpdateProgress(20);
      setUpdateMessage('Processing existing photos...');

      const existingPreviews = updateImagePreviews.filter(p => p.isExisting);
      for (const preview of existingPreviews) {
        if (preview.url_index !== undefined && Array.isArray(photoToUpdate.photo_url)) {
          photo_urls.push(photoToUpdate.photo_url[preview.url_index]);
          photo_file_paths.push(photoToUpdate.photo_file_path[preview.url_index]);
          // Remove this file from the delete list since we're keeping it
          const index = filesToDelete.indexOf(photoToUpdate.photo_file_path[preview.url_index]);
          if (index > -1) {
            filesToDelete.splice(index, 1);
          }
        } else if (!Array.isArray(photoToUpdate.photo_url)) {
          photo_urls.push(photoToUpdate.photo_url);
          photo_file_paths.push(photoToUpdate.photo_file_path);
          // Remove from delete list
          const index = filesToDelete.indexOf(photoToUpdate.photo_file_path);
          if (index > -1) {
            filesToDelete.splice(index, 1);
          }
        }
      }

      // Delete old files that were removed
      if (filesToDelete.length > 0) {
        await deletePhotosFromStorage(filesToDelete);
      }

      // 2. Upload new files
      if (updateSelectedFiles.length > 0) {
        setUpdateProgress(30);
        setUpdateMessage('Uploading new photos...');
      }

      for (let i = 0; i < updateSelectedFiles.length; i++) {
        const file = updateSelectedFiles[i];

        // Update progress for each file
        const progressPerFile = 40 / Math.max(updateSelectedFiles.length, 1);
        const currentProgress = 30 + (progressPerFile * i);
        setUpdateProgress(currentProgress);
        setUpdateMessage(`Uploading photo ${i+1} of ${updateSelectedFiles.length}...`);

        // Generate a unique filename using UUID
        const uniqueId = uuidv4();
        const uniqueFileName = `${updatePhotoId}-${uniqueId}.jpg`; // Always jpg after compression
        const filePath = `${userId}/${uniqueFileName}`;

        // Upload file to 'photos' bucket
        const { error: uploadError } = await supabase.storage
          .from('photos')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true
          });

        if (uploadError) {
          throw new Error(t('upload_error').replace('{count}', (i+1).toString()).replace('{message}', uploadError.message));
        }

        // Get the public URL
        const { data: urlData } = supabase.storage
          .from('photos')
          .getPublicUrl(filePath);

        if (!urlData?.publicUrl) {
          throw new Error(t('url_error'));
        }

        // Store the URL and path
        photo_urls.push(urlData.publicUrl);
        photo_file_paths.push(filePath);
      }

      // 3. Update the record via API
      setUpdateProgress(70);
      setUpdateMessage('Updating database record...');

      const updateResponse = await fetch('/api/knowledge/photos', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          photo_id: updatePhotoId,
          photo_url: photo_urls,
          photo_file_path: photo_file_paths
        })
      });

      const updateResult = await updateResponse.json();

      if (!updateResponse.ok) {
        throw new Error(updateResult.error || t('save_error').replace('{message}', 'Failed to update photo'));
      }

      // Update session storage cache
      const cached = getPhotosFromCache();
      if (cached) {
        const updatedPhotos = cached.data.map(p => 
          p.id === photoToUpdate.id 
            ? {
                ...p,
                photo_url: photo_urls,
                photo_file_path: photo_file_paths,
                updated_at: new Date().toISOString()
              }
            : p
        );
        // Sort photos by updated_at before updating cache
        const sortedPhotos = updatedPhotos.sort((a, b) => {
          const dateA = a.updated_at ? new Date(a.updated_at).getTime() : 0;
          const dateB = b.updated_at ? new Date(b.updated_at).getTime() : 0;
          return dateB - dateA;
        });
        updatePhotosInCache(sortedPhotos);
      }

      // Reset form
      setShowUpdateForm(false);
      setPhotoToUpdate(null);
      setUpdatePhotoId('');
      setUpdateSelectedFiles([]);
      setUpdateImagePreviews([]);

      // Show success message
      setUpdateProgress(100);
      setUpdateStatus('success');
      setUpdateMessage(t('photos_uploaded'));

      // Auto-dismiss after delay
      setTimeout(() => {
        setUpdateStatus('idle');
      }, 1500);

    } catch (error) {
      console.error('Error updating photo:', error);
      setUpdateError(t('error_text') + ': ' + (error instanceof Error ? error.message : 'Unknown error'));

      // Show error in the progress overlay
      setUpdateStatus('error');
      setUpdateMessage(t('error_text') + ': ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to open image preview with all available images
  const openImagePreview = (images: string[], initialIndex: number = 0) => {

    // Ensure we're working with valid images
    const validImages = images.filter(url => url && url.trim() !== '');

    if (validImages.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    if (initialIndex >= validImages.length) {
      console.warn(`Initial index ${initialIndex} is out of bounds, resetting to 0`);
      initialIndex = 0;
    }

    setPreviewImages(validImages);
    setCurrentImageIndex(initialIndex);
    setPreviewImage(validImages[initialIndex]);
    setIsZoomed(false); // Reset zoom state

    // Pre-load all images to avoid flashing during navigation
    validImages.forEach((url) => {
      const img = new Image();
      img.src = url;
    });
  };

  // Navigate to previous image
  const showPreviousImage = () => {
    if (previewImages.length <= 1) return;
    const newIndex = (currentImageIndex - 1 + previewImages.length) % previewImages.length;
    setCurrentImageIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
  };

  // Navigate to next image
  const showNextImage = () => {
    if (previewImages.length <= 1) return;
    const newIndex = (currentImageIndex + 1) % previewImages.length;
    setCurrentImageIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
  };

  // Add keyboard navigation for photo gallery
  useEffect(() => {
    if (previewImage) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          showPreviousImage();
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          showNextImage();
        } else if (event.key === 'Escape') {
          event.preventDefault();
          setPreviewImage(null);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [previewImage, showPreviousImage, showNextImage]);

  // Touch event handlers for swipe navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
    setTouchEnd(null);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      showNextImage();
    }

    if (isRightSwipe) {
      showPreviousImage();
    }
  };

  // Load initial data - only after client info is available
  useEffect(() => {
    if (clientInfo) {
      // Clear any stale flags from sessionStorage
      sessionStorage.removeItem('showUploadFormAfterDiscard');
    }
  }, [clientInfo]); // Remove refetchPhotos from dependencies

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (photoIdCheckTimeout) {
        clearTimeout(photoIdCheckTimeout);
      }
    };
  }, [photoIdCheckTimeout]);

  // Clean up sessionStorage when component unmounts
  useEffect(() => {
    return () => {
      sessionStorage.removeItem('showUploadFormAfterDiscard')
    }
  }, [])

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.headerBackground} rounded-2xl px-4 py-3 border ${themeConfig.headerBorder} ${themeConfig.headerHoverBorder} transition-all duration-300 overflow-hidden`}
          >


            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <LinkButton
                href="/dashboard"
                variant="secondary"
                size="sm"
                className={`inline-flex items-center text-sm ${theme === 'light' ? 'bg-white border border-gray-300 text-zinc-900 hover:bg-gray-50' : ''}`}
                leftIcon={
                  <svg
                    className="w-4 h-4 -ml-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
              >
                {t('back')}
              </LinkButton>

              <h1 className={`text-2xl md:text-3xl font-extrabold font-title ${themeConfig.textPrimary}`}>
                {t('photo_page_title')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-3 sm:p-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Statistics</h2> */}

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow1">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill={themeConfig.statCircleBackground}
                        stroke={themeConfig.statCircleBorder}
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke={themeConfig.statCircleTrack}
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray={`${Math.min((totalFaqs / (totalFaqsLimit || 1)) * 251.2, 251.2)} 251.2`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                        filter="url(#glow1)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full ${themeConfig.skeletonElement} border ${themeConfig.inputBorder} flex items-center justify-center`} style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('brain')}</p>

                  {/* Count */}
                  <p className={`${themeConfig.textPrimary} text-xs sm:text-base font-body`}>
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.textPrimary}`}></span>
                      </span>
                      : <>{totalFaqs} <span className={themeConfig.textMuted}>/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Photo Gallery Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow2">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill={themeConfig.statCircleBackground}
                        stroke={themeConfig.statCircleBorder}
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke={themeConfig.statCircleTrack}
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 - Math.min((photoCount / (photoLimit || 1)) * 251.2, 251.2)}
                        transform="rotate(-90 50 50)"
                        filter="url(#glow2)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full ${themeConfig.skeletonElement} border ${themeConfig.inputBorder} flex items-center justify-center`} style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('photo_gallery')}</p>

                  {/* Count */}
                  <p className={`${themeConfig.textPrimary} text-xs sm:text-base font-body`}>
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.textPrimary}`}></span>
                      </span>
                      : <>{photoCount} <span className={themeConfig.textMuted}>/ {photoLimit || 0}</span></>
                    }
                  </p>
                </div>
              </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-3 sm:p-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Knowledge Management</h2> */}

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <LinkButton
                  href="/dashboard/knowledge"
                  variant="secondary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={typeof window !== 'undefined' && window.location.pathname === '/dashboard/knowledge'}
                >
                  {t('business_insight')}
                </LinkButton>

                <LinkButton
                  href="/dashboard/knowledge/photo"
                  variant="primary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={typeof window !== 'undefined' && window.location.pathname === '/dashboard/knowledge/photo'}
                >
                  {t('photo_gallery')}
                </LinkButton>

                <LinkButton
                  href="/dashboard/knowledge/intro"
                  variant="secondary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={typeof window !== 'undefined' && window.location.pathname === '/dashboard/knowledge/intro'}
                >
                  {t('intros_outros')}
                </LinkButton>

                {/* <Link
                  href="/dashboard/knowledge/productList"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product List
                </Link> */}
              </div>
              </div>
            </div>
          </div>

          {/* Photo Gallery Content Section */}
          <div
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}
          >

            <div className="relative z-10">
            <div className="flex justify-between items-center mb-2">
              <h2 className={`text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title ${themeConfig.textPrimary}`}>{t('photo_page_title')}</h2>
              <Button
                onClick={toggleUploadForm}
                variant="primary"
                size="sm"
                className="text-xs sm:text-base"
              >
                {showUploadForm ? t('cancel') : t('add_photos')}
              </Button>
            </div>

            {/* Upload Form */}
            {showUploadForm && (
              <div
                className={`relative mb-6 p-4 ${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-2xl overflow-hidden`}
              >

                <div className="relative z-10">
                <h3 className="text-sm font-semibold mb-3 font-title">{t('add_photos')}</h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Photo ID Input */}
                  <div>
                    <label htmlFor="photoId" className="block text-xs text-zinc-400 mb-1">{t('photo_id')}</label>
                    <div className="relative">
                      <input
                        type="text"
                        id="photoId"
                        value={photoId}
                        onChange={handlePhotoIdChange}
                        onBlur={handlePhotoIdBlur}
                        className={`w-full ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} ${themeConfig.inputText} rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-gray-300 ${
                          photoIdExists ? 'border-red-500/50' :
                          photoId.trim() && !isCheckingPhotoId && !photoIdExists ? 'border-green-500/50' :
                          themeConfig.inputBorder
                        }`}
                        placeholder={t('enter_photo_id')}
                        required
                      />

                      {/* Validation Icon */}
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                        {isCheckingPhotoId ? (
                          <div className="w-4 h-4 border-2 border-zinc-400 border-t-transparent rounded-full animate-spin"></div>
                        ) : photoId.trim() && photoIdExists ? (
                          <FaTimes className="w-4 h-4 text-red-500" />
                        ) : photoId.trim() && !photoIdExists ? (
                          <FaCheck className="w-4 h-4 text-green-500" />
                        ) : null}
                      </div>
                    </div>

                    {/* Validation Message */}
                    {photoIdValidationMessage && (
                      <p className={`text-[10px] sm:text-xs mt-1 ${photoIdExists ? 'text-red-500' : 'text-green-500'}`}>
                        {photoIdValidationMessage}
                      </p>
                    )}
                  </div>

                  {/* Photo Upload Area */}
                  <div>
                    <label className={`block text-xs ${themeConfig.textMuted} mb-1`}>{t('max_files')}</label>
                    <div
                      ref={dropAreaRef}
                      className={`${theme === 'light' ? 'bg-white' : 'bg-black/30'} border-2 border-dashed ${themeConfig.inputBorder} rounded-lg p-4 flex flex-col items-center justify-center ${imagePreviews.length === 0 ? 'hover:border-jade-purple/75 transition-colors cursor-pointer' : ''} relative`}
                      onClick={() => {
                        // Only trigger file selection if there are no images or we're processing
                        if (imagePreviews.length === 0 && !isProcessingImage) {
                          document.getElementById('photo-file')?.click();
                        }
                      }}
                    >
                      {isProcessingImage ? (
                        <div className="flex flex-col items-center justify-center py-4 md:py-2">
                          <div className="w-10 h-10 border-3 border-jade-purple border-t-transparent rounded-full animate-spin mb-2"></div>
                          <p className={`${themeConfig.textMuted} text-[10px] sm:text-xs`}>{t('processing_image')}</p>
                        </div>
                      ) : imagePreviews.length > 0 ? (
                        <div className="w-full py-2">
                          {/* Image Preview Grid - Responsive layout */}
                          <div key="image-preview-grid" className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-2">
                            {imagePreviews.map((preview, index) => (
                              <div key={`preview-${preview.id}`} className="relative">
                                <div className="aspect-square overflow-hidden rounded-lg border border-zinc-700">
                                  <img
                                    key={`preview-img-${preview.id}`}
                                    src={preview.url}
                                    alt={`Preview ${index + 1}`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeFile(index);
                                  }}
                                  className={`absolute top-1 right-1 w-6 h-6 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} hover:bg-red-500 hover:text-white`}
                                >
                                  <FaTrash className="w-3 h-3" />
                                </Button>
                                <p className={`${themeConfig.textMuted} text-[8px] mt-1 truncate`}>
                                  {selectedFiles[index]?.name.substring(0, 15)}
                                  {selectedFiles[index]?.name.length > 15 ? '...' : ''}
                                </p>
                              </div>
                            ))}

                            {/* Add More Button - Only show if less than 4 images */}
                            {imagePreviews.length < 4 && (
                              <div
                                className={`border-2 border-dashed ${themeConfig.inputBorder} rounded-lg flex flex-col items-center justify-center aspect-square hover:border-jade-purple transition-colors cursor-pointer`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  document.getElementById('photo-file')?.click();
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className={`h-8 w-8 ${themeConfig.textMuted} mb-1`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <p className={`${themeConfig.textMuted} text-[10px] text-center`}>{t('add_more')}</p>
                              </div>
                            )}
                          </div>

                          <p className={`${themeConfig.textMuted} text-[10px] sm:text-xs text-center`}>
                            {imagePreviews.length} {t('of_text')} 4 {t('photo_count')}
                          </p>
                        </div>
                      ) : (
                        <div className="py-4 md:py-3 flex flex-col items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-zinc-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <p className="text-zinc-500 text-[10px] sm:text-xs text-center">
                            {t('drop_files_here')}<br/>
                            <span className="text-[8px] sm:text-[10px]">{t('max_files')}</span>
                          </p>
                        </div>
                      )}
                      <input
                        id="photo-file"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        className="hidden"
                        onChange={handleFileChange}
                        multiple
                      />
                    </div>
                    {uploadError && (
                      <p className="text-red-500 text-[10px] sm:text-xs mt-1">{uploadError}</p>
                    )}
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end space-x-3">
                    <Button
                      type="button"
                      onClick={handleCancel}
                      variant="secondary"
                      size="sm"
                      className="text-xs sm:text-base"
                    >
                      {t('cancel')}
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                      size="sm"
                      disabled={isUploading || selectedFiles.length === 0 || !photoId.trim() || photoIdExists || isCheckingPhotoId}
                      className="text-xs sm:text-base"
                      isLoading={isUploading}
                      loadingText={t('uploading')}
                    >
                      {t('upload_photos')}
                    </Button>
                  </div>
                </form>
                </div>
              </div>
            )}

            {/* Update Form */}
            {showUpdateForm && photoToUpdate && (
              <div
                className={`relative mb-6 p-4 ${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-2xl overflow-hidden`}
              >

                <div className="relative z-10">
                <h3 className={`text-sm font-semibold mb-3 font-title ${themeConfig.textPrimary}`}>{t('edit')} {t('photo_page_title')}</h3>
                <form onSubmit={showUpdateConfirmation} className="space-y-4">
                  {/* Photo ID Input - Read-only */}
                  <div>
                    <label htmlFor="updatePhotoId" className="block text-xs text-zinc-400 mb-1">{t('photo_id')} ({t('photo_id_title')})</label>
                    <input
                      type="text"
                      id="updatePhotoId"
                      value={updatePhotoId}
                      readOnly
                      className={`w-full ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} ${themeConfig.inputText} rounded-lg px-3 py-2 text-base focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-gray-300 cursor-not-allowed opacity-70`}
                      placeholder={t('enter_photo_id')}
                      required
                    />
                  </div>

                  {/* Photo Upload Area */}
                  <div>
                    <label className={`block text-xs ${themeConfig.textMuted} mb-1`}>{t('max_files')}</label>
                    <div
                      ref={updateDropAreaRef}
                      className={`border-2 border-dashed border-zinc-700 rounded-lg p-4 flex flex-col items-center justify-center relative`}
                    >
                      {isProcessingImage ? (
                        <div className="flex flex-col items-center justify-center py-4 md:py-2">
                          <div className="w-10 h-10 border-3 border-jade-purple border-t-transparent rounded-full animate-spin mb-2"></div>
                          <p className={`${themeConfig.textMuted} text-[10px] sm:text-xs`}>{t('processing_image')}</p>
                        </div>
                      ) : updateImagePreviews.length > 0 ? (
                        <div className="w-full py-2">
                          {/* Image Preview Grid - Responsive layout */}
                          <div key="image-preview-grid" className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-2">
                            {updateImagePreviews.map((preview, index) => (
                              <div key={`preview-${preview.id}`} className="relative">
                                <div className="aspect-square overflow-hidden rounded-lg border border-zinc-700">
                                  <img
                                    key={`preview-img-${preview.id}`}
                                    src={preview.url}
                                    alt={`Preview ${index + 1}`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeUpdateFile(index);
                                  }}
                                  className={`absolute top-1 right-1 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} ${themeConfig.textPrimary} p-1 rounded-full hover:bg-red-500 hover:text-white transition-colors`}
                                >
                                  <FaTrash className="w-3 h-3" />
                                </button>
                                <p className={`${themeConfig.textMuted} text-[8px] mt-1 truncate`}>
                                  {preview.isExisting ? t('existing_photo') : updateSelectedFiles[index - updateImagePreviews.filter(p => p.isExisting).length]?.name.substring(0, 15)}
                                </p>
                              </div>
                            ))}

                            {/* Add More Button - Only show if less than 4 images */}
                            {updateImagePreviews.length < 4 && (
                              <div
                                className={`border-2 border-dashed ${themeConfig.inputBorder} rounded-lg flex flex-col items-center justify-center aspect-square hover:border-jade-purple transition-colors cursor-pointer`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  document.getElementById('update-photo-file')?.click();
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className={`h-8 w-8 ${themeConfig.textMuted} mb-1`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                <p className={`${themeConfig.textMuted} text-[10px] text-center`}>{t('add_more')}</p>
                              </div>
                            )}
                          </div>

                          <p className={`${themeConfig.textMuted} text-[10px] sm:text-xs text-center`}>
                            {updateImagePreviews.length} {t('of_text')} 4 {t('photo_count')}
                          </p>
                        </div>
                      ) : (
                        <div className="py-4 md:py-3 flex flex-col items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-zinc-500 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <p className="text-zinc-500 text-[10px] sm:text-xs text-center">
                            {t('no_photos_added')}
                          </p>
                        </div>
                      )}
                      <input
                        id="update-photo-file"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        className="hidden"
                        onChange={handleUpdateFileChange}
                        multiple
                      />
                    </div>
                    {updateError && (
                      <p className="text-red-500 text-[10px] sm:text-xs mt-1">{updateError}</p>
                    )}
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end space-x-3">
                    <Button
                      type="button"
                      onClick={handleCancelUpdate}
                      variant="secondary"
                      className="text-xs sm:text-base"
                    >
                      {t('cancel')}
                    </Button>
                    <Button
                      type="submit"
                      disabled={isUpdating || updateImagePreviews.length === 0 || !updatePhotoId.trim() || !hasUpdateFormChanges()}
                      isLoading={isUpdating}
                      loadingText={t('processing_image')}
                      variant="primary"
                      className="text-xs sm:text-base"
                    >
                      {t('update_photo')}
                    </Button>
                  </div>
                </form>
                </div>
              </div>
            )}

            {/* Empty State - Show only when no photos */}
            {photoCount === 0 && (
              <div className={`text-center py-10 ${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg mb-6`}>
                <svg xmlns="http://www.w3.org/2000/svg" className={`h-12 w-12 mx-auto ${themeConfig.textMuted} mb-3`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body`}>{t('no_photos_added')}</p>
                <p className={`${themeConfig.textMuted} text-[8px] sm:text-[10px] mt-1`}>{t('click_add_photos')}</p>
              </div>
            )}

            {/* Photo Catalog with Search */}
            {photoCount > 0 && (
              <div>
                {/* Search Bar - Only show when not loading */}
                {!isLoadingPhotos && (
                  <div className="mb-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder={t('search_photo_id')}
                        value={searchQuery}
                        onChange={handleSearchChange}
                        className={`w-full ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} ${themeConfig.inputText} rounded-lg px-4 py-2 pl-10 text-base focus:outline-none focus:border-gray-500 hover:border-gray-500`}

                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${themeConfig.textMuted}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                )}

                {/* Photo List */}
                {isLoadingPhotos ? (
                  <>
                    {/* Loading header */}
                    <div className="py-8 flex justify-center items-center">
                      <div className="flex flex-col items-center space-y-4">
                        {/* Main loading spinner */}
                        <div className="relative">
                          <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
                          <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                        </div>

                        {/* Loading text with animation */}
                        <div className="text-center">
                          <p className="text-white text-lg font-medium mb-1">{t('loading_photos')}</p>
                        </div>

                        {/* Progress indicator */}
                        <div className={`w-48 h-1 ${themeConfig.skeletonElement} rounded-full overflow-hidden`}>
                          <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    {/* Skeleton loading for photo list */}
                    <div className="space-y-2">
                      {Array.from({ length: 6 }).map((_, index) => (
                        <div key={`skeleton-${index}`} className={`${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg p-3 flex items-center`}>
                          {/* Skeleton thumbnail */}
                          <div className={`w-16 h-16 ${themeConfig.skeletonElement} rounded-lg animate-pulse`} style={{ animationDelay: `${index * 0.1}s` }}></div>

                          {/* Skeleton content */}
                          <div className="ml-3 flex-grow space-y-2">
                            <div className={`h-4 ${themeConfig.skeletonElement} rounded animate-pulse w-3/4`} style={{ animationDelay: `${index * 0.15}s` }}></div>
                            <div className={`h-3 ${themeConfig.skeletonElement} rounded animate-pulse w-1/2`} style={{ animationDelay: `${index * 0.2}s` }}></div>
                            <div className={`h-3 ${themeConfig.skeletonElement} rounded animate-pulse w-1/3`} style={{ animationDelay: `${index * 0.25}s` }}></div>
                          </div>

                          {/* Skeleton action buttons */}
                          <div className="flex space-x-2">
                            <div className={`w-8 h-8 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.3}s` }}></div>
                            <div className={`w-8 h-8 ${themeConfig.skeletonElement} rounded animate-pulse`} style={{ animationDelay: `${index * 0.35}s` }}></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                ) : filteredPhotos.length > 0 ? (
                  <div>
                    {/* Vertical List Layout for Photos */}
                    <div className="space-y-2 mb-4">
                      {getCurrentPageItems().map((photo, index) => (
                        <div 
                          key={`photo-item-${photo.photo_id}-${index}`} 
                          className={`border ${themeConfig.cardBorder} hover:border-gray-500 rounded-lg p-3 flex items-center`}
                        >
                          {/* Thumbnail - Optimized */}
                          <PhotoThumbnail
                            key={`photo-thumbnail-${photo.photo_id}-${index}`}
                            photo={{
                              photo_url: Array.isArray(photo.photo_url) ? photo.photo_url : [photo.photo_url],
                              photo_id: photo.photo_id
                            }}
                            className="w-16 h-16 rounded-lg border border-white/30"
                            onClick={() => {
                              // Open photo gallery
                              const urls = Array.isArray(photo.photo_url) ? photo.photo_url : [photo.photo_url];
                              openImagePreview(urls, 0);
                            }}
                          />

                          {/* Photo Info */}
                          <div className="ml-3 flex-grow">
                            <h3 className={`text-sm font-semibold ${themeConfig.textPrimary}`}>{photo.photo_id}</h3>
                            <p className={`text-xs ${themeConfig.textSecondary}`}>
                              {Array.isArray(photo.photo_url) ? photo.photo_url.length : 1} {t('photo_count')}
                            </p>
                            <p className={`text-[10px] ${themeConfig.textMuted}`}>
                              {new Date(photo.updated_at).toLocaleDateString()}
                            </p>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditClick(photo)}
                              className={`w-8 h-8 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} hover:border-jade-purple-dark`}
                              title={t('edit')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ${themeConfig.textPrimary}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteClick(photo)}
                              className="w-8 h-8 bg-red-600 hover:bg-red-700 text-white"
                              title={t('delete')}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Pagination Controls */}
                    {totalPages > 1 && (
                      <div className="flex justify-center items-center space-x-2 mt-6 mb-2">
                        <Button
                          onClick={handlePreviousPage}
                          disabled={currentPage === 1}
                          variant="secondary"
                          size="icon"
                          className="h-8 w-8"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </Button>

                        <div className="flex items-center space-x-2">
                          {renderPaginationButtons()}
                        </div>

                        <Button
                          onClick={handleNextPage}
                          disabled={currentPage === totalPages}
                          variant="secondary"
                          size="icon"
                          className="h-8 w-8"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Button>
                      </div>
                    )}

                    {/* Photo count summary */}
                    {filteredPhotos.length > 0 && (
                      <div className={`mt-4 text-[8px] sm:text-xs ${themeConfig.textMuted} text-center`}>
                        {t('showing_text')} {Math.min(filteredPhotos.length, (currentPage - 1) * itemsPerPage + 1)} - {Math.min(filteredPhotos.length, currentPage * itemsPerPage)} {t('of_text')} {filteredPhotos.length} {t('photo_count')}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={`text-center py-10 ${themeConfig.cardBackground} border ${themeConfig.cardBorder} rounded-lg`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-12 w-12 mx-auto ${themeConfig.textMuted} mb-3`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body`}>{t('no_photos_found')}</p>
                    <p className={`${themeConfig.textMuted} text-[8px] sm:text-[10px] mt-1`}>{t('try_different_search')}</p>
                  </div>
                )}
              </div>
            )}
            </div>
          </div>
        </motion.div>
      </div>
      <Footer />

      {/* Cancel Confirmation Modal */}
      {showCancelConfirm && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={cancelConfirmRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('discard_changes_title')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('photos_selected').replace('{count}', selectedFiles.length.toString()).replace('{plural}', selectedFiles.length !== 1 ? 's' : '')}
              </p>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowCancelConfirm(false)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={confirmCancel}
                  variant="danger"
                  size="md"
                  className="flex-1"
                >
                  {t('discard_button')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Confirmation Modal */}
      {showUploadConfirm && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={uploadConfirmRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('upload_confirm').replace('{count}', selectedFiles.length.toString()).replace('{plural}', selectedFiles.length !== 1 ? 's' : '')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('upload_confirm_message')
                  .replace('{count}', selectedFiles.length.toString())
                  .replace('{plural}', selectedFiles.length !== 1 ? 's' : '')
                  .replace('{id}', photoId)}
              </p>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowUploadConfirm(false)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={handleConfirmedUpload}
                  variant="primary"
                  size="md"
                  className="flex-1"
                >
                  {t('confirm_upload')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Status Overlay */}
      {updateStatus !== 'idle' && (
        <div
          ref={statusOverlayRef}
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
          onClick={() => updateStatus !== 'loading' && setUpdateStatus('idle')}
        >
          <div
            className={`relative ${
              updateStatus === 'loading' 
                ? 'bg-jade-purple-dark/[0.6] border-white/20' 
                : updateStatus === 'success'
                  ? 'bg-green-900/[0.8] border-green-500/50'
                  : 'bg-red-900/[0.8] border-red-500/50'
            } rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className={`absolute inset-0 bg-gradient-to-br ${
              updateStatus === 'loading'
                ? 'from-jade-purple/10'
                : updateStatus === 'success'
                  ? 'from-green-500/10'
                  : 'from-red-500/10'
            } to-transparent opacity-50 rounded-2xl`}></div>
            <div className="relative z-10">
              {updateStatus === 'loading' ? (
                <div className="flex flex-col items-center text-center">
                  <div className="w-10 h-10 mb-4 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                  <p className={`text-lg font-semibold mb-3 ${themeConfig.textPrimary}`}>{t('processing_upload')}</p>
                  <div className="w-full bg-black/30 rounded-full h-3 mb-1">
                    <div
                      className="bg-white h-3 rounded-full transition-all duration-300"
                      style={{ width: `${updateProgress}%` }}
                    ></div>
                  </div>
                  <p className={`text-sm ${themeConfig.textSecondary}`}>{updateProgress}% {t('complete_text')}</p>
                  <p className={`text-sm mt-2 ${themeConfig.textSecondary}`}>{updateMessage}</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                    updateStatus === 'success' ? 'bg-green-900/[0.8]' : 'bg-red-900/[0.8]'
                  }`}>
                    {updateStatus === 'success' ? (
                      <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <p className={`text-lg font-semibold mb-1 ${themeConfig.textPrimary}`}>
                    {updateStatus === 'success' ? t('success_text') : t('error_text')}
                  </p>
                  <p className={`${themeConfig.textSecondary} text-center text-sm`}>{updateMessage}</p>
                  <Button
                    onClick={() => setUpdateStatus('idle')}
                    variant={updateStatus === 'success' ? 'success' : 'danger'}
                    size="sm"
                    className="mt-4"
                  >
                    {t('close')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Cancel Update Confirmation Modal */}
      {showCancelUpdateConfirm && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={cancelUpdateConfirmRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('discard_changes_title')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('unsaved_changes')}
              </p>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowCancelUpdateConfirm(false)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  variant="danger"
                  size="md"
                  className="flex-1"
                  onClick={() => {
                    setShowCancelUpdateConfirm(false);
                    closeUpdateForm();
                    const showUploadForm = sessionStorage.getItem('showUploadFormAfterDiscard');
                    if (showUploadForm === 'true') {
                      sessionStorage.removeItem('showUploadFormAfterDiscard');
                      setTimeout(() => {
                        setShowUploadForm(true);
                      }, 100);
                    }
                  }}
                >
                  {t('discard_button')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Update Confirmation Modal */}
      {showUpdateConfirm && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={updateConfirmRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('update_photo_confirm')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('update_photo_message')}
              </p>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowUpdateConfirm(false)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={handleUpdateSubmit}
                  variant="primary"
                  size="md"
                  className="flex-1"
                >
                  {t('yes_update')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={deleteConfirmRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
            <div className="flex justify-center mb-4">
              <div className={`rounded-full p-3 ${isDeleting ? 'bg-red-700/80 text-red-200' : deleteSuccess ? 'bg-black/10 text-white' : 'bg-red-600/80 text-white'}`}>
                {isDeleting ? (
                  <div className="w-6 h-6 border-2 border-red-200 border-t-transparent rounded-full animate-spin"></div>
                ) : deleteSuccess ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                )}
              </div>
            </div>

            <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
              {isDeleting ? t('deleting') : deleteSuccess ? t('delete_success_title') : t('delete_photo')}
            </h3>

            {!isDeleting && !deleteSuccess && (
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('delete_photo_confirm').replace('{id}', photoToDelete?.photo_id || '')}
              </p>
            )}

            {isDeleting && (
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('deleting_photo')}
              </p>
            )}

            {deleteSuccess && (
              <p className={`${themeConfig.textSecondary} mb-6 text-center font-medium`}>
                {t('delete_success_message')}
              </p>
            )}

            <div className="flex space-x-3">
              {!isDeleting && !deleteSuccess && (
                <>
                  <Button
                    onClick={cancelDelete}
                    variant="secondary"
                    size="md"
                    className="flex-1"
                  >
                    {t('cancel')}
                  </Button>
                  <Button
                    onClick={confirmDeletePhoto}
                    variant="danger"
                    size="md"
                    className="flex-1"
                  >
                    {t('yes_delete')}
                  </Button>
                </>
              )}
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Remove Photo Confirmation Modal */}
      {showRemovePhotoConfirm && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={removePhotoConfirmRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('remove_photo_title')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('remove_photo_message')}
              </p>
              <div className="flex space-x-3">
                <Button
                  onClick={cancelRemoveFile}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={confirmRemoveFile}
                  variant="danger"
                  size="md"
                  className="flex-1"
                >
                  {t('yes_remove')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cannot Delete Photo Modal */}
      {showCannotDeleteModal && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            ref={cannotDeleteModalRef}
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              {/* Close button (X) */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2 h-7 w-7 rounded-full bg-black/20 hover:bg-white/20 text-white/70 hover:text-white"
                onClick={() => setShowCannotDeleteModal(false)}
                aria-label="Close"
              >
                <FaTimes />
              </Button>
              <div className="flex justify-center mb-4">
                <FaExclamationTriangle className="w-6 h-6 text-red-600" />
              </div>
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('cannot_delete_photo')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-4 text-center`}>
                {t('photo_in_use')}
              </p>
              {linkedQuestions.length > 0 && (
                <div className={`mb-6 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} rounded-lg p-3 max-h-40 overflow-y-auto`}>
                  <p className={`${themeConfig.textMuted} text-sm mb-2`}>{t('linked_questions')}:</p>
                  <ul className={`list-disc pl-5 ${themeConfig.textSecondary} text-sm`}>
                    {linkedQuestions.map((question, index) => (
                      <li key={`linked-question-${index}`} className="mb-1">{question}</li>
                    ))}
                  </ul>
                </div>
              )}
              <div className="flex justify-center">
                <button
                  onClick={() => setShowCannotDeleteModal(false)}
                  className={`px-6 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} hover:${themeConfig.cardHoverBorder} border ${themeConfig.inputBorder} ${themeConfig.textPrimary} rounded-lg transition-colors`}
                >
                  {t('close')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Gallery Modal */}
      {previewImage && (
        <div
          key="preview-modal"
          className="fixed inset-0 bg-black bg-opacity-20 backdrop-blur-lg flex items-center justify-center z-50"
        >
          <div
            key="preview-container"
            ref={imageGalleryRef}
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 w-full max-w-3xl mx-4 border ${themeConfig.cardBorder} overflow-hidden`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div key="preview-content" className="relative z-10">
            {/* Close button in the top-right corner */}
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute top-0 right-0 p-1.5 border border-white/20 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex flex-col">
              {/* Image counter */}
              <div className="text-center mb-2 text-sm text-zinc-400">
                {currentImageIndex + 1} / {previewImages.length}
              </div>

              {/* Main image container with touch events */}
              <div
                className="relative h-[60vh] flex items-center justify-center"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                aria-live="polite"
                role="region"
                aria-label={`Image ${currentImageIndex + 1} of ${previewImages.length}`}
              >
                {previewImages.length === 0 ? (
                  <div className="text-center text-zinc-400 p-8 bg-zinc-800/50 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p>{t('no_images_available')}</p>
                  </div>
                ) : (
                  <div className="relative">
                    <img
                      src={optimizeGalleryImage(previewImage)}
                      alt={`Image ${currentImageIndex + 1} of ${previewImages.length}`}
                      className="rounded-lg max-w-full max-h-full object-contain"
                      loading="eager"
                      decoding="async"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                      }}
                    />
                  </div>
                )}

                {/* Navigation buttons - only show if more than one image */}
                {previewImages.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={showPreviousImage}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label={t('previous_page')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={showNextImage}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label={t('next_page')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
              </div>

              {/* Thumbnail strip - only show if more than one image */}
              {previewImages.length > 1 && (
                <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                  {previewImages.map((url, index) => (
                    <PhotoThumbnail
                      key={`preview-thumbnail-${index}`}
                      photo={{
                        photo_url: [url],
                        photo_id: `thumbnail-${index + 1}`
                      }}
                      className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                        index === currentImageIndex ? 'border-white/80 scale-105 opacity-100' : 'border-zinc-700 opacity-70 hover:opacity-90 hover:border-zinc-600'
                      }`}
                      onClick={() => {
                        setCurrentImageIndex(index);
                        setPreviewImage(url);
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}