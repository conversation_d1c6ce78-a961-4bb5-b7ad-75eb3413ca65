'use client'

import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useDashboardData, updateFaqCountInCache, usePhotosData } from '@/hooks/useOptimizedData';
import { FaBrain, FaImage } from 'react-icons/fa';
import { useLanguage } from '@/context/LanguageContext';
import { useTheme, useThemeConfig } from '@/context/ThemeContext';
import { optimizeGalleryImage } from '@/utils/imageOptimization';
import { motion } from 'framer-motion';
import { Button, LinkButton } from '@/components/ui'; // Import our new Button components
import LanguageSwitcher from '@/components/LanguageSwitcher';
import ThemeAwareFooter from '@/components/ThemeAwareFooter';

// Memoized dummy data for knowledge base questions
const useDummyQuestions = () => {
  return useMemo(() =>
    Array.from({ length: 90 }, (_, i) => ({
      id: i + 1,
      question: `How does your product ${i % 3 === 0 ? 'handle customer data security' : i % 3 === 1 ? 'compare to competitors' : 'benefit my business'}? ${i % 5 === 0 ? 'I need detailed information about the features, benefits, and limitations.' : ''}`,
      answer: `Our product ${i % 3 === 0 ? 'uses end-to-end encryption to secure all customer data and complies with GDPR and other privacy regulations' : i % 3 === 1 ? 'offers 24/7 support, better pricing, and more features than competitors in the same category' : 'increases efficiency by 40% and reduces operational costs significantly'}. ${i % 4 === 0 ? 'We also provide comprehensive documentation and training materials to help you get the most out of our services.' : ''}`,
      dateAdded: new Date(2025, 3, Math.floor(Math.random() * 30) + 1).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' }),
      isActive: Math.random() > 0.2
    })),
    [] // Empty dependency array ensures data is only created once
  );
}

// Define the type for uploaded PDFs including the File object
// type UploadedPdf = {
//   id: number;
//   name: string;
//   size: string;
//   date: string;
//   file: File; // Add the File object
// };

// Define the type for photo info in QA items
interface PhotoInfo {
  id: number;
  photo_id: string;
  photo_url: string | null; // Single thumbnail for display
  full_photo_urls: string[] | null; // Complete array of photos
}

// Define the type for editing item
interface EditItem {
  id: number;
  field: 'question' | 'answer';
  value: string;
}

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonElement} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 ${themeConfig.skeletonElement} transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

export default function KnowledgePage() {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()

  // Use dashboard cache for knowledge stats - single source of truth
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  const [isUploading, setIsUploading] = useState(false)
  const [question, setQuestion] = useState<string>('')
  const [answer, setAnswer] = useState<string>('')
  const [recentQA, setRecentQA] = useState<Array<{
    id: number,
    question: string,
    answer: string,
    photoInfo?: PhotoInfo
  }>>([])
  const [isUpdating, setIsUpdating] = useState(false)
  const [editingItem, setEditingItem] = useState<EditItem | null>(null)
  const [hasFocusedInput, setHasFocusedInput] = useState<boolean>(false)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<number | null>(null)
  const [updateStatus, setUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateMessage, setUpdateMessage] = useState('')
  const [updateProgress, setUpdateProgress] = useState(0)

  // Get knowledge stats from dashboard cache instead of separate state
  const totalFaqs = knowledgeStats?.faqCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const faqUsagePercentage = knowledgeStats?.faqUsagePercentage || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const photoLimit = knowledgeStats?.photoLimit || 0
  const photoUsagePercentage = knowledgeStats?.photoUsagePercentage || 0
  const isLoadingCount = isDashboardLoading

  // Add new state for photo search
  const [photoSearchQuery, setPhotoSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [photoSearchResults, setPhotoSearchResults] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])
  const [selectedPhoto, setSelectedPhoto] = useState<{
    id: number,
    photo_id: string,
    photo_url: string | null,
    full_photo_urls: string[] | null
  } | null>(null)
  const [isPhotoLoading, setIsPhotoLoading] = useState(false)
  const [showPhotoResults, setShowPhotoResults] = useState(false)
  const searchResultsRef = useRef<HTMLDivElement>(null)

  const modalRef = useRef<HTMLDivElement>(null)
  const faqStatusOverlayRef = useRef<HTMLDivElement>(null); // Keep this one
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const questionInputRef = useRef<HTMLInputElement>(null)
  const answerInputRef = useRef<HTMLInputElement>(null)

  // Viewing popup state
  const [viewingItem, setViewingItem] = useState<{field: 'question' | 'answer', value: string} | null>(null)
  const viewModalRef = useRef<HTMLDivElement>(null)

  // Image gallery state
  const [imageGallery, setImageGallery] = useState<{urls: string[], currentIndex: number} | null>(null)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)

  // Remove PDF upload state
  // const [uploadedPdfs, setUploadedPdfs] = useState<Array<{id: number, name: string, size: string, date: string, file: File}>>([]);
  // const fileInputRef = useRef<HTMLInputElement>(null)
  // const [dragActive, setDragActive] = useState(false)

  // Remove state for document saving
  // const [isDocUpdating, setIsDocUpdating] = useState(false)
  // const [showDocConfirmation, setShowDocConfirmation] = useState(false)
  // const [docUpdateStatus, setDocUpdateStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  // const [docUpdateMessage, setDocUpdateMessage] = useState('')
  // const [docUpdateProgress, setDocUpdateProgress] = useState(0)



  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Batch state updates
      if (editingItem.id === -1) {
        setQuestion(editingItem.value);
      } else if (editingItem.id === -2) {
        setAnswer(editingItem.value);
      } else {
        // Use functional update to avoid dependency on recentQA
        setRecentQA(prev =>
          prev.map(qa =>
            qa.id === editingItem.id
              ? { ...qa, [editingItem.field]: editingItem.value }
              : qa
          )
        );
      }

      // Close modal after state updates
      setEditingItem(null);
      setHasFocusedInput(false);
    });
  }, [editingItem]);







  // Add effect to focus textarea and set cursor at the end when editing
  useEffect(() => {
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // On desktop: auto-focus and set cursor at the end
        textareaRef.current.focus()
        const length = textareaRef.current.value.length
        textareaRef.current.setSelectionRange(length, length)
        setHasFocusedInput(true)
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput])

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  // Function to focus input and set cursor at the end
  const focusInputAtEnd = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.focus()
      const length = ref.current.value.length
      ref.current.setSelectionRange(length, length)
    }
  }

  const handleStartEdit = (id: number, field: 'question' | 'answer', value: string) => {
    setEditingItem({ id, field, value })
    setHasFocusedInput(false)
    // Focus and set cursor at end will happen in useEffect
  }

  // handleSaveEdit is now defined above with useCallback

  const handleViewItem = (field: 'question' | 'answer', value: string) => {
    setViewingItem({ field, value })
  }

  const knowledgeItems = [
    {
      id: 1,
      title: 'Company FAQ',
      itemCount: 24,
      dateAdded: 'Apr 12, 2025',
      isActive: true
    },
    {
      id: 2,
      title: 'Product Information',
      itemCount: 36,
      dateAdded: 'Apr 10, 2025',
      isActive: true
    },
    {
      id: 3,
      title: 'Return Policy',
      itemCount: 8,
      dateAdded: 'Apr 5, 2025',
      isActive: false
    }
  ]

  const handleAddQA = () => {
    if (question.trim() && answer.trim()) {
      setRecentQA(prev => [...prev, {
        id: Date.now(),
        question: question.trim(),
        answer: answer.trim(),
        // Include photo info if a photo is selected
        photoInfo: selectedPhoto ? {
          id: selectedPhoto.id,
          photo_id: selectedPhoto.photo_id,
          photo_url: selectedPhoto.photo_url,
          full_photo_urls: selectedPhoto.full_photo_urls
        } : undefined
      }]);

      // Reset fields after adding
      setQuestion('');
      setAnswer('');
      // Reset the selected photo as well
      setSelectedPhoto(null);
    }
  }

  // Prompt for confirmation before update
  const handleUpdate = () => {
    if (recentQA.length === 0) {
      setUpdateMessage(t('no_questions_update'))
      setUpdateStatus('error')
      setTimeout(() => setUpdateStatus('idle'), 3000)
      return
    }
    setShowConfirmation(true)
  }

  // Note: blobToBase64 function is defined below in saveQAToSupabase

  // Helper function to convert blob to base64
  const blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onerror = reject;
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String.split(',')[1]); // Remove the data URL prefix
      };
      reader.readAsDataURL(blob);
    });
  }

  // Replace the entire existing saveQAToSupabase function with secure API approach
  const saveQAToSupabase = async () => {
    setShowConfirmation(false);
    setIsUpdating(true);
    setUpdateStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(t('processing_additions'));

    const totalItems = recentQA.length;
    if (totalItems === 0) {
      setUpdateMessage(t('no_questions_update'));
      setUpdateStatus('error');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      setIsUpdating(false);
      return;
    }

    // Start fake progress animation (clean increments: 10, 20, 30, etc.)
    const progressInterval = setInterval(() => {
      setUpdateProgress(prev => {
        if (prev >= 90) return prev; // Stop at 90% until real response
        return Math.min(90, prev + 10); // Clean 10% increments
      });
    }, 300);

    try {
      // Prepare knowledge items for API
      const knowledgeItems = []

      for (const qa of recentQA) {
        setUpdateMessage(`Processing "${qa.question.substring(0, 20)}..."`);

        knowledgeItems.push({
          question: qa.question,
          answer: qa.answer,
          photoInfo: qa.photoInfo
        });
      }

      // Call secure API
      const response = await fetch('/api/knowledge/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ knowledgeItems }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to save knowledge items');
      }

      if (!responseData.success) {
        throw new Error('Unexpected response from server');
      }

      // Clear progress interval and complete progress
      clearInterval(progressInterval);
      setUpdateProgress(100);
      setUpdateMessage(`Successfully processed ${responseData.itemsProcessed} knowledge items`);
      setUpdateStatus('success');

      // Update FAQ count in dashboard cache immediately
      const newFaqCount = totalFaqs + responseData.itemsProcessed;
      updateFaqCountInCache(newFaqCount);


      // Clear the recent QA list
      setRecentQA([]);

      // Show success message and reset
      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 1500);

    } catch (error: any) {
      console.error('Error updating knowledge base:', error);

      // Clear progress interval on error
      clearInterval(progressInterval);

      setUpdateStatus('error');
      setUpdateMessage(error.message || 'Failed to save knowledge items');

      setTimeout(() => {
        setUpdateStatus('idle');
        setIsUpdating(false);
      }, 3000);
    }
  };

  const handleDelete = (id: number) => {
    // Show confirmation modal instead of deleting immediately
    setItemToDelete(id);
    setShowDeleteConfirmation(true);
  }

  // Function to handle actual deletion after confirmation
  const confirmDelete = () => {
    if (itemToDelete === null) return;

    const qaToDelete = recentQA.find(qa => qa.id === itemToDelete);


    // Remove item after confirmation
    setRecentQA(prev => prev.filter(qa => qa.id !== itemToDelete));

    // Close the confirmation modal
    setShowDeleteConfirmation(false);
    setItemToDelete(null);
  }









  // Add useEffect to close product results when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchResultsRef.current && !searchResultsRef.current.contains(event.target as Node)) {
        setShowPhotoResults(false)
      }
    }

    if (showPhotoResults) {
      document.addEventListener('mousedown', handleClickOutside)
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showPhotoResults])

  // Add state for all photos
  const [allPhotos, setAllPhotos] = useState<Array<{
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }>>([])

  // Use optimized photos data hook with cache
  const { data: photosData, loading: photosLoading } = usePhotosData()

  // Add effect to fetch photos on page load - only after client info is available
  useEffect(() => {
    if (photosData) {
      setAllPhotos(photosData)
    }
  }, [photosData])

  // Function to fetch photos from supabase
  const searchPhotos = async (query: string) => {
    if (!query.trim()) {
      setPhotoSearchResults([])
      setShowPhotoResults(false)
      return
    }

    setIsSearching(true)
    try {
      // First try to search locally in cached photos
      if (allPhotos.length > 0) {
        const filteredPhotos = allPhotos.filter(photo =>
          photo.photo_id.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5)

        if (filteredPhotos.length > 0) {
          setPhotoSearchResults(filteredPhotos)
          setShowPhotoResults(true)
          setIsSearching(false)
          return
        }
      }

      // If no local results, search via secure API
      const response = await fetch('/api/knowledge/search-photos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, limit: 5 }),
      })

      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error searching photos:', responseData.error)
        return
      }

      // Transform API response to match expected format
      const transformedPhotos = responseData.photos.map((photo: any) => ({
        id: photo.id,
        photo_id: photo.photoId,
        photo_url: photo.allUrls,
        photo_file_path: photo.filePaths
      }))

      setPhotoSearchResults(transformedPhotos)
      setShowPhotoResults(transformedPhotos.length > 0)
    } catch (error) {
      console.error('Error in searchPhotos:', error)
    } finally {
      setIsSearching(false)
    }
  }

  // Add function to clear photo search
  const clearPhotoSearch = () => {
    setPhotoSearchResults([]);
    setShowPhotoResults(false);
    setPhotoSearchQuery('');
  };

  // Add function to handle photo search input
  const handlePhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setPhotoSearchQuery(query)
    if (query.trim()) {
      searchPhotos(query)
    } else {
      clearPhotoSearch()
    }
  }

  // Add function to select a photo
  const handleSelectPhoto = (photo: {
    id: number,
    photo_id: string,
    photo_url: string[] | null,
    photo_file_path: string[] | null
  }) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null

    // Show loading animation
    setIsPhotoLoading(true)

    // Clear search results and hide dropdown
    clearPhotoSearch()

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedPhoto({
        id: photo.id,
        photo_id: photo.photo_id,
        photo_url: thumbnail,
        full_photo_urls: photo.photo_url // Store the complete array of photo URLs
      })

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsPhotoLoading(false)
      }, 50)
    }, 100)
  }

  // Add function to clear selected photo
  const handleClearSelectedPhoto = () => {
    setSelectedPhoto(null)
    clearPhotoSearch()
  }


  // Handle viewing an image gallery
  const handleViewImage = (imageUrls: string[] | null | undefined) => {
    if (!imageUrls || imageUrls.length === 0) {
      // Show a notification that there are no images to display
      setUpdateStatus('error');
      setUpdateMessage('No images available for this item.');
      setTimeout(() => setUpdateStatus('idle'), 3000);
      return;
    }

    setImageGallery({
      urls: imageUrls,
      currentIndex: 0
    });
  };

  // Navigate to previous image in gallery
  const showPreviousImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  };

  // Navigate to next image in gallery
  const showNextImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({ ...imageGallery, currentIndex: newIndex });
  };

  // Handle touch events for swipe in gallery
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const minSwipeDistance = 50; // Minimum distance in pixels to be considered a swipe
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      // Handle left swipe (next image)
      showNextImage();
    }

    if (isRightSwipe) {
      // Handle right swipe (previous image)
      showPreviousImage();
    }

    // Reset touch coordinates
    setTouchStart(null);
    setTouchEnd(null);
  };

  // Keyboard navigation for image gallery (arrow keys and escape)
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (!imageGallery) return;

      if (event.key === 'ArrowLeft') {
        showPreviousImage();
      } else if (event.key === 'ArrowRight') {
        showNextImage();
      } else if (event.key === 'Escape') {
        setImageGallery(null);
      }
    }

    if (imageGallery) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    }
  }, [imageGallery]);

  // Simple scroll disable when any popup is open
  useEffect(() => {
    const hasOpenModal = showConfirmation || editingItem || viewingItem || imageGallery || showDeleteConfirmation || (updateStatus !== 'idle')

    if (hasOpenModal) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [showConfirmation, editingItem, viewingItem, imageGallery, showDeleteConfirmation, updateStatus])

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.headerBackground} rounded-2xl px-4 py-3 border ${themeConfig.headerBorder} ${themeConfig.headerHoverBorder} transition-all duration-300 overflow-hidden`}
          >
            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <LinkButton
                href="/dashboard"
                variant="secondary"
                size="sm"
                className={`inline-flex items-center text-sm ${theme === 'light' ? 'bg-white border border-gray-300 text-zinc-900 hover:bg-gray-50' : ''}`}
                leftIcon={
                  <svg
                    className="w-4 h-4 -ml-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
              >
                {t('back')}
              </LinkButton>

              <h1 className={`text-2xl md:text-3xl font-extrabold font-title ${themeConfig.textPrimary}`}>
                {t('ai_brain')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-3 sm:p-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Statistics</h2> */}

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow1">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill={themeConfig.statCircleBackground}
                        stroke={themeConfig.statCircleBorder}
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke={themeConfig.statCircleTrack}
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray={`${Math.min((totalFaqs / (totalFaqsLimit || 1)) * 251.2, 251.2)} 251.2`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                        filter="url(#glow1)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full ${themeConfig.skeletonElement} border ${themeConfig.inputBorder} flex items-center justify-center`} style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('brain')}</p>

                  {/* Count */}
                  <p className={`${themeConfig.textPrimary} text-xs sm:text-base font-body`}>
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.textPrimary}`}></span>
                      </span>
                      : <>{totalFaqs} <span className={themeConfig.textMuted}>/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Photo Gallery Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow2">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill={themeConfig.statCircleBackground}
                        stroke={themeConfig.statCircleBorder}
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke={themeConfig.statCircleTrack}
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 - Math.min((photoCount / (photoLimit || 1)) * 251.2, 251.2)}
                        transform="rotate(-90 50 50)"
                        filter="url(#glow2)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full ${themeConfig.skeletonElement} border ${themeConfig.inputBorder} flex items-center justify-center`} style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('photo_gallery')}</p>

                  {/* Count */}
                  <p className={`${themeConfig.textPrimary} text-xs sm:text-base font-body`}>
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.textPrimary}`}></span>
                      </span>
                      : <>{photoCount} <span className={themeConfig.textMuted}>/ {photoLimit || 0}</span></>
                    }
                  </p>
                </div>
              </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-3 sm:p-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Knowledge Management</h2> */}

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <LinkButton
                  href="/dashboard/knowledge"
                  variant="primary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={true}
                >
                  {t('business_insight')}
                </LinkButton>

                <LinkButton
                  href="/dashboard/knowledge/photo"
                  variant="secondary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={false}
                >
                  {t('photo_gallery')}
                </LinkButton>

                <LinkButton
                  href="/dashboard/knowledge/intro"
                  variant="secondary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={false}
                >
                  {t('intros_outros')}
                </LinkButton>

                {/* <Link
                  href="/dashboard/knowledge/productList"
                  className="bg-zinc-800 hover:bg-zinc-700 text-white py-2 sm:py-3 px-2 text-xs sm:text-base rounded-lg transition-colors inline-block font-body text-center"
                >
                  Product List
                </Link> */}
              </div>
              </div>
            </div>
          </div>

          {/* Business Insights Section */}
          <div
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}
          >

            <div className="relative z-10">
            <div className="flex justify-between items-center mb-2">
              <h2 className={`text-xl font-bold font-title ${themeConfig.textPrimary}`}>{t('business_insights')}</h2>
              <LinkButton
                href="/dashboard/knowledge/knowledgeBase"
                variant="secondary"
                size="sm"
                className="text-xs sm:text-base p-2 px-3"
              >
                {t('manage')}
              </LinkButton>
            </div>
            <p className={`${themeConfig.textSecondary} text-sm mb-4 md:mb-6 font-body`}>
              {t('add_business_info')}
            </p>

            {/* Photo Search Bar */}
            <div className="mb-4 relative">
              <div className="relative">
                <input
                  type="text"
                  value={photoSearchQuery}
                  onChange={handlePhotoSearch}
                  onFocus={() => {
                    if (photoSearchResults.length > 0) {
                      setShowPhotoResults(true);
                    } else if (photoSearchQuery) {
                      // If there's a query but no results, trigger a new search
                      searchPhotos(photoSearchQuery);
                    }
                  }}
                  placeholder={t('search_photo_placeholder')}
                  className={`w-full px-3 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-500 hover:border-gray-500`}
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Search Results Dropdown */}
              {showPhotoResults && (
                <div
                  className={`absolute z-50 w-full mt-2 rounded-xl shadow-2xl max-h-60 overflow-y-auto backdrop-blur-lg ${theme === 'light' ? 'bg-white border border-gray-300' : 'bg-white/20'}`}
                  ref={searchResultsRef}
                >
                  {photoSearchResults.length > 0 ? (
                    photoSearchResults.map((photo, index) => (
                      <div
                        key={`${photo.id}-${photo.photo_id}-${index}`}
                        className={`flex items-center gap-3 p-3 cursor-pointer border-b transition-colors duration-200 last:border-0 ${theme === 'light' ? 'hover:bg-gray-100 border-gray-200' : 'hover:bg-white/10 border-white/5'}`}
                        onClick={() => handleSelectPhoto(photo)}
                        style={{
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {/* Photo Thumbnail - Optimized */}
                        <PhotoThumbnail
                          photo={photo}
                          className="w-10 h-10"
                        />
                        {/* Photo ID */}
                        <div className="flex-1 truncate">
                          <p className={`${themeConfig.textPrimary} truncate`}>{photo.photo_id}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className={`p-3 ${themeConfig.textMuted} text-center`}>
                      {t('no_photos_found')}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Photo Display or Loading Animation */}
            {isPhotoLoading ? (
              <div className={`mb-4 p-4 rounded-xl flex items-center justify-center h-16 border ${theme === 'light' ? 'bg-gray-50 border-gray-300' : 'bg-white/10 border-white/10'}`}>
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  <span className={`${themeConfig.textSecondary} text-sm`}>{t('loading')}</span>
                </div>
              </div>
            ) : selectedPhoto && (
              <div className={`mb-4 p-3 rounded-xl flex items-center justify-between animate-fadeIn border transition-colors hover:border-gray-500 ${theme === 'light' ? 'border-gray-300' : 'border-white/30'}`}>
                <div className="flex items-center gap-3">
                  {/* Photo Thumbnail */}
                  <div className={`w-10 h-10 rounded overflow-hidden flex-shrink-0 border ${theme === 'light' ? 'bg-gray-200 border-gray-300' : 'bg-black/50 border-white/20'}`}>
                    {selectedPhoto.photo_url ? (
                      <img
                        src={selectedPhoto.photo_url}
                        alt={selectedPhoto.photo_id}
                        className="w-full h-full object-cover cursor-pointer"
                        onClick={() => handleViewImage(selectedPhoto.full_photo_urls || (selectedPhoto.photo_url ? [selectedPhoto.photo_url] : null))}
                      />
                    ) : (
                      <div className={`w-full h-full flex items-center justify-center ${theme === 'light' ? 'bg-gray-100' : 'bg-white/5'} ${themeConfig.textMuted}`}>
                        <span>No Image</span>
                      </div>
                    )}
                  </div>
                  {/* Photo ID */}
                  <div>
                    <p className={`${themeConfig.textPrimary}`}>{selectedPhoto.photo_id}</p>
                  </div>
                </div>
                {/* Remove Button */}
                <button
                  onClick={handleClearSelectedPhoto}
                  className={`p-1 rounded-full transition-colors duration-200 border ${theme === 'light' ? 'bg-gray-100 hover:bg-red-100 text-gray-600 hover:text-red-600 border-gray-300 hover:border-red-300' : 'bg-white/5 hover:bg-white/10 text-zinc-300 hover:text-white border-white/10'}`}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-12 gap-3 mb-6">
              {/* Question Trigger */}
              <div className="sm:col-span-5">
                <div
                  className={`px-2 md:px-4 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} font-body focus:outline-none focus:border-gray-300 cursor-pointer hover:border-gray-500 flex items-center min-h-[42px]`}
                  onClick={() => {
                    setEditingItem({
                      id: -1,
                      field: 'question',
                      value: question
                    });
                  }}
                >
                  {question ?
                    <span className="truncate">{question}</span> :
                    <span className={`${themeConfig.textMuted} truncate`}>{t('enter_question')}</span>
                  }
                </div>
              </div>
              {/* Answer Trigger */}
              <div className="sm:col-span-5">
                <div
                  className={`px-2 pr-12 md:px-4 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none focus:border-gray-300 cursor-pointer hover:border-gray-500 flex items-center min-h-[42px] relative`}
                  onClick={() => {
                    setEditingItem({
                      id: -2,
                      field: 'answer',
                      value: answer
                    });
                  }}
                >
                  {/* Regular input display */}
                  {answer ?
                    <span className="truncate pr-10">{answer}</span> :
                    <span className={`${themeConfig.textMuted} truncate pr-10`}>{t('enter_reply')}</span>
                  }
                </div>
              </div>
              {/* Add Button */}
              <div className="sm:col-span-2 flex items-center justify-center">
                <Button
                  onClick={handleAddQA}
                  variant="primary"
                  size="md"
                  className="text-xs sm:text-base w-full"
                  disabled={!question.trim() || !answer.trim()}
                >
                  {t('add')}
                </Button>
              </div>
            </div>

            {/* Recently Added Section - now part of the same container */}
            <div className={`flex justify-between items-center mb-3 border-t ${theme === 'light' ? 'border-gray-200' : 'border-white/10'} pt-6`}>
              <h3 className={`text-lg font-semibold font-title ${themeConfig.textPrimary}`}>{t('recently_added')}</h3>
              <Button
                onClick={handleUpdate}
                variant="primary"
                size="md"
                className="text-xs sm:text-base"
                disabled={isUpdating || recentQA.length === 0}
              >
                {isUpdating ? t('updating') : t('update')}
              </Button>
            </div>

            {/* Column Headers */}
            <div className={`flex border-b ${theme === 'light' ? 'border-gray-200' : 'border-white/10'} py-3 font-semibold ${themeConfig.textSecondary} font-body`}>
              <div className="w-[5%] px-2 text-left"></div>
              <div className="w-[35%] px-2 text-left">{t('question')}</div>
              <div className="w-[35%] px-2 text-left">{t('reply')}</div>
              <div className="w-[15%] px-2 text-left"></div>
              <div className="w-[10%] px-2 text-center"></div>
            </div>

            {/* List Items */}
            <div className="w-full">
              {recentQA.length > 0 ? (
                recentQA.map((qa, index) => (
                  <div key={qa.id} className={`flex border-b ${theme === 'light' ? 'border-gray-200' : 'border-white/10'} py-3 items-center`}>
                    <div className={`w-[5%] px-2 text-left ${themeConfig.textMuted} font-body`}>
                      {index + 1}
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      <div
                        className={`h-full w-full truncate break-words px-3 py-1.5 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${theme === 'light' ? 'hover:bg-gray-100' : 'hover:bg-black/40'} hover:border-gray-500 flex items-center group cursor-pointer transition-all`}
                        title={qa.question}
                        onClick={() => handleStartEdit(qa.id, 'question', qa.question)}
                      >
                        <span className={`flex-1 ${themeConfig.textPrimary}`}>{qa.question}</span>
                        <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                    </div>
                    <div
                      className="w-[35%] px-2 py-1"
                    >
                      <div
                        className={`h-full w-full truncate break-words px-3 py-1.5 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${theme === 'light' ? 'hover:bg-gray-100' : 'hover:bg-black/40'} hover:border-gray-500 flex items-center group cursor-pointer transition-all`}
                        title={qa.answer}
                        onClick={() => handleStartEdit(qa.id, 'answer', qa.answer)}
                      >
                        <span className={`flex-1 ${themeConfig.textPrimary}`}>{qa.answer}</span>
                        <svg className={`w-4 h-4 ml-2 ${themeConfig.textMuted} group-hover:text-jade-purple invisible group-hover:visible`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                    </div>
                    {/* Photo Column */}
                    <div className="w-[15%] px-2 py-1">
                      {qa.photoInfo ? (
                        <div className="h-full w-full px-3 py-1.5 rounded-lg flex items-center justify-center">
                          {/* Photo Thumbnail - Optimized and clickable to view gallery */}
                          <PhotoThumbnail
                            photo={{
                              photo_url: qa.photoInfo.photo_url ? [qa.photoInfo.photo_url] : null,
                              photo_id: qa.photoInfo.photo_id
                            }}
                            className="w-10 h-10 border border-white/20"
                            onClick={() => handleViewImage(qa.photoInfo?.full_photo_urls || (qa.photoInfo?.photo_url ? [qa.photoInfo.photo_url] : null))}
                          />
                        </div>
                      ) : null}
                    </div>
                    <div className="w-[10%] px-2 flex justify-center">
                      <button
                        className="p-2 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-500 transition-colors"
                        onClick={() => handleDelete(qa.id)}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className={`py-8 text-center ${themeConfig.textMuted}`}>
                  {t('no_questions_update')}
                </div>
              )}
            </div>
            </div>
          </div>

        </motion.div>
      </div>

      {/* FAQ Update Status Overlay */}
      {updateStatus !== 'idle' && (
        <div
          ref={faqStatusOverlayRef}
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
          onClick={() => updateStatus !== 'loading' && setUpdateStatus('idle')}
        >
          <div
            className={`relative ${
              updateStatus === 'loading' 
                ? `${theme === 'light' ? 'bg-white border-jade-purple/30' : 'bg-jade-purple-dark/[0.6] border-white/20'}` 
                : updateStatus === 'success'
                  ? `${theme === 'light' ? 'bg-white border-green-300' : 'bg-green-900/[0.8] border-green-500/50'}`
                  : `${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`
            } rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden`}
            onClick={(e) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className={`absolute inset-0 bg-gradient-to-br ${
                updateStatus === 'loading'
                  ? 'from-jade-purple/10'
                  : updateStatus === 'success'
                    ? 'from-green-500/10'
                    : 'from-red-500/10'
              } to-transparent opacity-50 rounded-2xl`}></div>
            )}

            <div className="relative z-10">
              {updateStatus === 'loading' ? (
                <div className="flex flex-col items-center text-center">
                  <div className={`w-10 h-10 mb-4 border-3 ${theme === 'light' ? 'border-jade-purple border-t-transparent' : 'border-white border-t-transparent'} rounded-full animate-spin`}></div>
                  <p className={`text-lg font-semibold mb-3 ${themeConfig.textPrimary}`}>{t('updating')}...</p>
                  <div className={`w-full ${theme === 'light' ? 'bg-gray-200' : 'bg-black/30'} rounded-full h-3 mb-1`}>
                    <div
                      className={`${theme === 'light' ? 'bg-jade-purple' : 'bg-white'} h-3 rounded-full transition-all duration-300`}
                      style={{ width: `${updateProgress}%` }}
                    ></div>
                  </div>
                  <p className={`text-sm ${themeConfig.textSecondary}`}>{updateProgress}% {t('complete')}</p>
                  <p className={`text-sm mt-2 ${themeConfig.textSecondary}`}>{updateMessage}</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                    updateStatus === 'success' 
                      ? `${theme === 'light' ? 'bg-green-100' : 'bg-green-900/[0.8]'}` 
                      : `${theme === 'light' ? 'bg-red-100' : 'bg-red-900/[0.8]'}`
                  }`}>
                    {updateStatus === 'success' ? (
                      <svg className={`w-8 h-8 ${theme === 'light' ? 'text-green-600' : 'text-green-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className={`w-8 h-8 ${theme === 'light' ? 'text-red-600' : 'text-red-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <p className={`text-lg font-semibold mb-1 ${themeConfig.textPrimary}`}>
                    {updateStatus === 'success' ? t('success') : t('error')}
                  </p>
                  <p className={`${themeConfig.textSecondary} text-center text-sm`}>{updateMessage}</p>
                  <Button
                    onClick={() => setUpdateStatus('idle')}
                    variant={updateStatus === 'success' ? 'success' : 'danger'}
                    size="sm"
                    className="mt-4"
                  >
                    {t('close')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('update')} {t('knowledge')} {t('management')}
              </h3>
              <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                {t('confirm_update_questions').replace('{count}', recentQA.length.toString()).replace('{plural}', recentQA.length !== 1 ? 's' : '')}
              </p>
              <div className="flex space-x-3">
                <Button
                  onClick={() => setShowConfirmation(false)}
                  variant="secondary"
                  size="md"
                  className="flex-1"
                >
                  {t('cancel')}
                </Button>
                <Button
                  onClick={saveQAToSupabase}
                  variant="primary"
                  size="md"
                  className="flex-1"
                >
                  {t('confirm')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modals */}
      {editingItem && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-[53]`}
        >
          <div
            ref={modalRef}
            className={`relative rounded-2xl p-6 w-full max-w-lg mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple-dark/[0.3] border-white/20'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10" data-modal-content>
              {/* Close button (X) */}
              <Button
                variant="ghost"
                size="icon"
                className={`absolute top-0 right-0 p-1 rounded-full ${theme === 'light' ? 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 border border-gray-300' : 'bg-black/40 hover:bg-jade-purple/30 text-white/60 hover:text-white border border-gray-300'}`}
                onClick={() => setEditingItem(null)}
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>

              <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                {t('edit')} {editingItem.field === 'question' ? t('question') : t('reply')}
              </h3>
              <textarea
                ref={textareaRef}
                value={editingItem.value}
                onChange={(e) => {
                  // Only update if value actually changed
                  if (e.target.value !== editingItem.value) {
                    setEditingItem(prev => prev ? {...prev, value: e.target.value} : null);
                  }
                }}
                onClick={() => {
                  // On mobile: only position cursor at end on FIRST click (initial focus)
                  // After that, allow free cursor movement
                  if (!hasFocusedInput && textareaRef.current) {
                    const length = textareaRef.current.value.length;
                    textareaRef.current.setSelectionRange(length, length);
                    setHasFocusedInput(true);
                  }
                  // Subsequent clicks: let user position cursor freely (default browser behavior)
                }}
                placeholder={editingItem.field === 'question' ? t('enter_question') : t('enter_reply')}
                className={`w-full px-4 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border-2 ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none min-h-[100px] mb-4`}
              />
              <Button
                onClick={handleSaveEdit}
                variant="primary"
                size="md"
                className="w-full"
              >
                {t('done')}
              </Button>
            </div>
          </div>
        </div>
      )}
      {viewingItem && (
         <div
           className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
         >
           <div
             ref={viewModalRef}
             className={`relative rounded-2xl p-6 w-full max-w-lg mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple-dark/[0.3] border-white/20'}`}
             onClick={(e: React.MouseEvent) => e.stopPropagation()}
           >

             <div className="relative z-10">
             <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
               {viewingItem.field === 'question' ? t('question') : t('reply')}
             </h3>
             <div className={`${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg p-4 ${themeConfig.textPrimary} mb-4 max-h-[60vh] overflow-y-auto`}>
               {viewingItem.value}
             </div>
              <Button
                onClick={() => setViewingItem(null)}
                variant="secondary"
                size="md"
                className="w-full"
              >
                {t('close')}
              </Button>
             </div>
           </div>
         </div>
      )}


      <ThemeAwareFooter />


      {/* Image Gallery Modal */}
      {imageGallery && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 backdrop-blur-lg flex items-center justify-center z-50"
        >
          <div
            ref={imageGalleryRef}
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 w-full max-w-3xl mx-4 border ${themeConfig.cardBorder} overflow-hidden`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >

            <div className="relative z-10">
            {/* Close button in the top-right corner */}
            <button
              onClick={() => setImageGallery(null)}
              className="absolute top-0 right-0 p-1.5 border border-white/20 rounded-full bg-zinc-800 hover:bg-jade-purple text-zinc-400 hover:text-white transition-colors z-20"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="flex flex-col">
              {/* Image counter */}
              <div className="text-center mb-2 text-sm text-zinc-400">
                {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
              </div>

              {/* Main image container with touch events */}
              <div
                className="relative h-[60vh] flex items-center justify-center"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                aria-live="polite"
                role="region"
                aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
              >
                {imageGallery.urls.length === 0 ? (
                  <div className="text-center text-zinc-400 p-8 bg-zinc-800/50 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-zinc-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p>{t('no_images_available')}</p>
                  </div>
                ) : (
                  <div className="relative">
                    <img
                      src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                      alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                      className="rounded-lg max-w-full max-h-full object-contain"
                      loading="eager"
                      decoding="async"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                      }}
                    />
                  </div>
                )}

                {/* Navigation buttons - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={showPreviousImage}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Previous image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={showNextImage}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Next image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
              </div>

              {/* Thumbnail strip - only show if more than one image */}
              {imageGallery.urls.length > 1 && (
                <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                  {imageGallery.urls.map((url, index) => (
                    <PhotoThumbnail
                      key={index}
                      photo={{
                        photo_url: [url],
                        photo_id: `thumbnail-${index + 1}`
                      }}
                      className={`w-16 h-16 rounded-lg border-2 transition-all duration-200 ease-in-out ${
                        index === imageGallery.currentIndex ? 'border-white/80 scale-105 opacity-100' : 'border-zinc-700 opacity-70 hover:opacity-90 hover:border-zinc-600'
                      }`}
                      onClick={() => {
                        setImageGallery({ ...imageGallery, currentIndex: index });
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
            </div>
          </div>
        </div>
      )}

      {/* Editing Item Modal */}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && (
        <div
          className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
        >
          <div
            className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            {theme === 'dark' && (
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
            )}
            <div className="relative z-10">
            <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
              {t('delete_item')}
            </h3>

            <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
              {t('delete_confirmation')}
            </p>

            <div className="flex justify-between w-full space-x-4">
              <Button
                onClick={() => setShowDeleteConfirmation(false)}
                variant="secondary"
                size="md"
                className="flex-1"
              >
                {t('cancel')}
              </Button>
              <Button
                onClick={confirmDelete}
                variant="danger"
                size="md"
                className="flex-1"
              >
                {t('delete')}
              </Button>
            </div>
            </div>
          </div>
        </div>
      )}

    </div>
  )
}