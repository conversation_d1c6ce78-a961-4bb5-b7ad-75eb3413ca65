'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { LinkButton, Button } from '@/components/ui';
import { motion } from 'framer-motion'
import Footer from '@/components/Footer'
import { createClientComponentClient } from '@/utils/supabase/client'
import { useDashboardData, usePhotosData } from '@/hooks/useOptimizedData'
import { FaBrain, FaImage, FaComments, FaTrash, FaExclamationTriangle, FaCheck, FaTimes, FaSave, FaUndo } from 'react-icons/fa';
import { v4 as uuidv4 } from 'uuid'

import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { optimizeGalleryImage } from '@/utils/imageOptimization'

// TypeScript interfaces
interface PhotoData {
  id: number
  photo_id: string
  photo_url: string[] | null
  photo_file_path: string[] | null
}

interface SelectedPhoto {
  id: number
  photo_id: string
  photo_url: string | null
  full_photo_urls: string[] | null
}

interface ImageGallery {
  urls: string[]
  currentIndex: number
}

interface EditingItem {
  value: string
}

// Optimized PhotoThumbnail component for fast loading without Vercel Image
const PhotoThumbnail = ({
  photo,
  className = "w-10 h-10",
  onClick
}: {
  photo: { photo_url: string[] | null, photo_id: string },
  className?: string,
  onClick?: () => void
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const themeConfig = useThemeConfig()

  return (
    <div
      className={`${className} ${themeConfig.skeletonElement} rounded overflow-hidden flex-shrink-0 relative ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      {photo.photo_url && photo.photo_url.length > 0 && !imageError ? (
        <>
          {/* Loading placeholder - no animation */}
          <div className={`absolute inset-0 ${themeConfig.skeletonElement} transition-opacity duration-200 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`} />

          {/* Actual image */}
          <img
            src={photo.photo_url[0]}
            alt={photo.photo_id}
            className={`w-full h-full object-cover transition-opacity duration-200 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
            loading="lazy"
            decoding="async"
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true)
              setImageLoaded(true)
            }}
          />
        </>
      ) : (
        <div className={`w-full h-full flex items-center justify-center ${themeConfig.skeletonElement} ${themeConfig.textMuted} text-xs`}>
          <svg width="24" height="18" viewBox="0 0 24 18" fill="currentColor">
            <rect width="24" height="18" rx="2" fill="currentColor" opacity="0.3"/>
            <path d="M3 13L7 9L11 13L15 6L21 12V15H3V13Z" fill="currentColor" opacity="0.6"/>
            <circle cx="7" cy="6" r="2" fill="currentColor" opacity="0.6"/>
          </svg>
        </div>
      )}
    </div>
  )
}

export default function IntroPage() {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  
  // Use dashboard cache for knowledge stats and client info
  const { data: dashboardData, loading: isDashboardLoading } = useDashboardData()
  const clientInfo = dashboardData?.clientInfo
  const knowledgeStats = dashboardData?.knowledgeStats

  // Use optimized photos data hook
  const { data: photosData, loading: photosLoading } = usePhotosData()

  // Get knowledge stats from dashboard cache
  const totalFaqs = knowledgeStats?.faqCount || 0
  const photoCount = knowledgeStats?.photoCount || 0
  const totalFaqsLimit = knowledgeStats?.faqLimit || 0
  const photoLimit = knowledgeStats?.photoLimit || 0

  const isLoadingCount = isDashboardLoading

  // Create Supabase client (used only for storage operations - audio uploads/deletions)
  const supabase = createClientComponentClient()

  // Photo search state - only for intro
  const [introSearchQuery, setIntroSearchQuery] = useState('')
  const [introSearchResults, setIntroSearchResults] = useState<PhotoData[]>([])
  const [showIntroResults, setShowIntroResults] = useState(false)
  const [isSearchingIntro, setIsSearchingIntro] = useState(false)
  const [selectedIntroPhoto, setSelectedIntroPhoto] = useState<SelectedPhoto | null>(null)

  // Loading animation states for photo selection
  const [isIntroPhotoLoading, setIsIntroPhotoLoading] = useState(false)
  const introSearchResultsRef = useRef<HTMLDivElement>(null)
  const introSearchInputRef = useRef<HTMLInputElement>(null)

  // Add state for all photos
  const [allPhotos, setAllPhotos] = useState<PhotoData[]>([])

  // Image gallery state
  const [imageGallery, setImageGallery] = useState<ImageGallery | null>(null)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const imageGalleryRef = useRef<HTMLDivElement>(null)




  // Text content state
  const [introText, _setIntroText] = useState('')

  // Custom setters to track changes
  const setIntroText = (value: string) => {
    _setIntroText(value);
  }

  const [isIntroSaving, setIsIntroSaving] = useState(false)

  // Loading state for intro data
  const [isLoadingIntroData, setIsLoadingIntroData] = useState(true)

  // Edit mode state
  const [isIntroEditing, setIsIntroEditing] = useState(false)
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false)
  // Removed confirmationSection since we only have intro
  const [saveStatus, setSaveStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle')
  const [updateProgress, setUpdateProgress] = useState(0)
  const [updateMessage, setUpdateMessage] = useState('')

  // Initial state tracking for detecting changes
  const [initialIntroText, setInitialIntroText] = useState('')
  const [initialSelectedIntroPhoto, setInitialSelectedIntroPhoto] = useState<SelectedPhoto | null>(null)

  // Store actual chat_ids from database
  const [introChatId, setIntroChatId] = useState<string | null>(null)
  const [hasIntroChanges, setHasIntroChanges] = useState(false)

  // Editing state
  const [editingItem, setEditingItem] = useState<EditingItem | null>(null)

  // Track if textarea has been focused for mobile behavior
  const [hasFocusedInput, setHasFocusedInput] = useState(false)

  // Ref for textarea auto-focus
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)
  

  // Removed manual fetch functions - now using dashboard cache

  // Clear intro photo search
  const clearIntroPhotoSearch = () => {
    setIntroSearchResults([]);
    setShowIntroResults(false);
    setIntroSearchQuery('');
  };

  // Handle intro photo search
  const handleIntroPhotoSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setIntroSearchQuery(query);
    
    if (query.trim()) {
      searchIntroPhotos(query);
    } else {
      clearIntroPhotoSearch();
    }
  };



  // Search photos for intro section
  const searchIntroPhotos = async (query: string) => {
    if (!query.trim()) {
      setIntroSearchResults([]);
      setShowIntroResults(false);
      return;
    }

    setIsSearchingIntro(true);
    try {
      // Use cached photos data
      if (photosData) {
        const filteredPhotos = photosData
          .filter(photo =>
            photo.photo_id.toLowerCase().includes(query.toLowerCase())
          )
          .slice(0, 5)

        setIntroSearchResults(filteredPhotos)
        setShowIntroResults(filteredPhotos.length > 0)
      } else {
        // If no cached data, fetch from API
        const searchResponse = await fetch('/api/knowledge/photos', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        const searchData = await searchResponse.json()

        if (!searchResponse.ok) {
          console.error('Error searching photos:', searchData.error)
          return
        }

        const filteredPhotos = (searchData.photos || [])
          .filter((photo: any) =>
            photo.photo_id.toLowerCase().includes(query.toLowerCase())
          )
          .slice(0, 5)

        setIntroSearchResults(filteredPhotos)
        setShowIntroResults(filteredPhotos.length > 0)
      }
    } catch (error) {
      console.error('Error in searchIntroPhotos:', error)
    } finally {
      setIsSearchingIntro(false)
    }
  };



  // Handle selecting a photo from intro search results
  const handleSelectIntroPhoto = (photo: PhotoData) => {
    // Get first photo URL as thumbnail for display, but store the full array
    const thumbnail = photo.photo_url && photo.photo_url.length > 0 ? photo.photo_url[0] : null;
    
    // Clear search results and hide dropdown
    clearIntroPhotoSearch();

    const processedPhoto = {
      id: photo.id,
      photo_id: photo.photo_id,
      photo_url: thumbnail,
      full_photo_urls: photo.photo_url // Store the complete array of photo URLs
    };

    // Show loading animation
    setIsIntroPhotoLoading(true);

    // Hide dropdown and clear search query immediately
    setShowIntroResults(false);
    setIntroSearchQuery(''); // Clear the search query after selection

    // Add a small delay to show the loading animation
    setTimeout(() => {
      // Set the selected photo
      setSelectedIntroPhoto(processedPhoto);

      // Update change tracking
      const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
      setHasIntroChanges(
        initialPhotoId !== photo.photo_id ||
        introText !== initialIntroText ||
        (introAudioUrl !== initialIntroAudioUrl)
      );

      // Hide loading animation after a short delay
      setTimeout(() => {
        setIsIntroPhotoLoading(false);
      }, 50);
    }, 100);
  };



  // Clear selected photo
  const handleClearSelectedPhoto = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedIntroPhoto(null);
    setIntroSearchQuery('');

    // Update change tracking
    const hadInitialPhoto = initialSelectedIntroPhoto !== null;
    setHasIntroChanges(
      hadInitialPhoto ||
      introText !== initialIntroText ||
      (introAudioUrl !== initialIntroAudioUrl)
    );
  };

  // View image in gallery
  const handleViewImage = (urls: string[] | null) => {
    if (!urls || urls.length === 0) {
      console.error('No images available to view');
      return;
    }

    // Filter out any invalid URLs
    const validUrls = urls.filter(url => url && url.trim() !== '');

    if (validUrls.length === 0) {
      console.error('No valid images found to preview');
      return;
    }

    // Open the gallery modal
    setImageGallery({
      urls: validUrls,
      currentIndex: 0
    });
  };

  // Navigate to previous image
  const showPreviousImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex - 1 + imageGallery.urls.length) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
  };

  // Navigate to next image
  const showNextImage = () => {
    if (!imageGallery || imageGallery.urls.length <= 1) return;

    const newIndex = (imageGallery.currentIndex + 1) % imageGallery.urls.length;
    setImageGallery({
      ...imageGallery,
      currentIndex: newIndex
    });
  };

  // Touch event handlers for swipe navigation
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
    setTouchEnd(null);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd || !imageGallery) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      showNextImage();
    }

    if (isRightSwipe) {
      showPreviousImage();
    }
  };



















  // Handle text input for intro
  const handleTextChange = (value: string) => {
    setIntroText(value);
    // Update change tracking
    setHasIntroChanges(
      value !== initialIntroText ||
      (selectedIntroPhoto !== initialSelectedIntroPhoto)
    );
  };

  // Show save confirmation popup
  const showSaveConfirmationPopup = () => {
    // Check if there's valid content before showing the confirmation
    const hasValidContent = hasValidIntroContent();
    const hasChanges = hasIntroChanges;

    if (!hasValidContent) {
      console.warn(`Cannot save intro without text or audio content`);
      return;
    }

    if (!hasChanges) {
      console.warn(`No changes to save for intro`);
      return;
    }

    setShowSaveConfirmation(true);
  };

  // Check if intro has changes
  const checkIntroChanges = (): boolean => {
    // Check text changes
    if (introText !== initialIntroText) return true;

    // Check photo changes
    const initialPhotoId = initialSelectedIntroPhoto?.photo_id;
    const currentPhotoId = selectedIntroPhoto?.photo_id;
    if ((initialPhotoId && !currentPhotoId) || (!initialPhotoId && currentPhotoId) ||
        (initialPhotoId && currentPhotoId && initialPhotoId !== currentPhotoId)) return true;

    return false;
  };



  // Check if intro has valid content (text only)
  const hasValidIntroContent = (): boolean => {
    return introText.trim() !== '';
  };



  // Reset intro to initial state
  const resetIntroToInitial = () => {
    // Reset text to initial value
    setIntroText(initialIntroText);
    setSelectedIntroPhoto(initialSelectedIntroPhoto);
    setIntroSearchQuery(''); // Reset search query
    setHasIntroChanges(false);
  };



  // Handle cancel edit
  const handleCancelEdit = () => {
    const hasChanges = checkIntroChanges();

    if (hasChanges) {
      // Show confirmation dialog
      setShowCancelConfirmation(true);
    } else {
      // No changes, just exit edit mode
      setIsIntroEditing(false);
    }
  };

  // Confirm cancel with reset
  const confirmCancel = () => {
    // Use the normal reset function
    resetIntroToInitial();
    setIsIntroEditing(false);

    // Close the confirmation dialog
    setShowCancelConfirmation(false);
  };





  // Handle edit button click
  const handleEdit = () => {
    // Save initial state for tracking changes
    setInitialIntroText(introText);
    setInitialSelectedIntroPhoto(selectedIntroPhoto ? {...selectedIntroPhoto} : null);
    setHasIntroChanges(false);
    setIsIntroEditing(true);
  };




  // Save intro settings to welcome_chat table
  const handleSave = async () => {
    // Close the confirmation dialog
    setShowSaveConfirmation(false);

    // Set the appropriate saving state
    setIsIntroSaving(true);
    setIsIntroEditing(false);


    // Set initial loading state
    setSaveStatus('loading');
    setUpdateProgress(0);
    setUpdateMessage(`Preparing to save intro message...`);

    try {
      const clientId = clientInfo?.client_id;

      if (!clientId) {
        console.error('Client ID not found while saving intro');
        setUpdateMessage('Client ID not found. Please try again or contact support.');
        setSaveStatus('error');
        return;
      }

      // Use the actual chat_id from database
      const chatId = introChatId;

      if (!chatId) {
        console.error(`No intro chat_id found. Record may not exist.`);
        setUpdateMessage(`Intro record not found. Please refresh the page.`);
        setSaveStatus('error');
        return;
      }

      // Prepare data for database update
      let updateData: any = {
        client_id: clientId,
        chat_id: chatId
      };

      // Update progress
      setUpdateProgress(20);
      setUpdateMessage('Processing data...');



      setUpdateProgress(40);

      // Use answer_p field for updates (consistent with other knowledge pages)
      const currentText = introText;
      updateData.answer_p = currentText;

      // Add photo data if selected
      if (selectedIntroPhoto) {
        updateData.photo_url = selectedIntroPhoto.full_photo_urls;
        updateData.photo_id = selectedIntroPhoto.photo_id;
      }

      // Save text to answer_p field
      updateData.answer_p = introText;

      setUpdateProgress(70);
      setUpdateMessage('Saving to database...');

      // Check if record still exists (it might have changed since we checked earlier)
      setUpdateProgress(80);

      // Determine what content has changed to handle ATM ID fields
      const hasPhotoChanged = (selectedIntroPhoto?.photo_id !== initialSelectedIntroPhoto?.photo_id);

      // Handle ATM ID fields based on what changed
      if (hasPhotoChanged) {
        // Photo changed: clear photo ATM IDs
        updateData.fb_photo_atmid = null;
        updateData.ig_photo_atmid = null;
        updateData.tg_photo_atmid = null;
      }
      // If only text changed, no ATM ID fields need to be cleared

      // Always update existing record (intro generated during registration)
      setUpdateMessage('Updating record...');
      try {
        const updateResponse = await fetch('/api/knowledge/welcome-chat', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            chat_id: chatId,
            updateData: updateData
          })
        });

        const updateResult = await updateResponse.json();

        if (!updateResponse.ok) {
          throw new Error(updateResult.error || 'Failed to update record');
        }
      } catch (error) {
        console.error('Error updating record:', error);
        throw new Error('Failed to update record');
      }

      setUpdateProgress(90);
      setUpdateMessage(`Intro message saved successfully!`);

      // Trigger webhook for all updates (text, audio, and photo changes)
      // TODO: Temporarily commented out - not sending anything to this webhook currently
      /*
      try {

        // Find the original data to compare changes
        const originalPhoto = initialSelectedIntroPhoto;
        const currentPhoto = selectedIntroPhoto;

        // Determine if photo has changed
        const hasPhotoChanged =
          // Photo added (didn't exist before but exists now)
          (currentPhoto && !originalPhoto) ||
          // Photo removed (existed before but not now)
          (!currentPhoto && originalPhoto) ||
          // Photo changed (different photo ID)
          (currentPhoto && originalPhoto &&
           originalPhoto.photo_id !== currentPhoto.photo_id);

        // Get current text and audio URL
        const currentText = introText;
        const audioUrl = updateData.audio_url || "";

        // Prepare webhook data
        const welcomeData = {
          chat_id: chatId,
          client_id: clientId,
          answer: currentText,
          sector,
          lang: lang || 'en', // Send language so webhook knows which fields were updated
          audio_url: audioUrl,
          is_photo: hasPhotoChanged
        };

        // Send webhook for all updates
        await sendWelcomeUpdateWebhook(welcomeData);
        setUpdateProgress(100);
        setUpdateMessage(`Intro message saved!`);
      } catch (webhookError) {
        console.error('Error sending welcome webhook:', webhookError);
        // Don't fail the save operation if webhook fails
        setUpdateProgress(100);
      }
      */

      setUpdateProgress(100);
      setUpdateMessage(`Intro message saved!`);

      // Set success status
      setSaveStatus('success');

    } catch (error: any) {
      console.error('Error saving intro:', error);
      setUpdateMessage(error.message || `Error saving intro message. Please try again.`);
      setSaveStatus('error');
    } finally {
      // Reset the appropriate saving state based on the section
      setTimeout(() => {
        setIsIntroSaving(false);

        // Reset save status after a delay
        setTimeout(() => {
          setSaveStatus('idle');
        }, 1500);
      }, 1000);
    }
  };

  // Load existing intro data
  const loadIntroData = async () => {
    try {
      // Wait for dashboard data to load before fetching intro data
      if (!clientInfo?.client_id) {
        return
      }

      // Set loading state
      setIsLoadingIntroData(true)

      // Fetch all intro data for this client via API
      const response = await fetch('/api/knowledge/welcome-chat')
      const responseData = await response.json()

      if (!response.ok) {
        console.error('Error fetching welcome data:', responseData.error);
        return;
      }

      const welcomeData = responseData.welcomeData;

      // Get intro data based on chat_id pattern
      const introData = welcomeData?.find((record: any) => record.chat_id.endsWith('-1'));

      // Set intro data if exists
      if (introData) {
        // Store the actual chat_id from database
        setIntroChatId(introData.chat_id);

        // Use answer_p field with fallback to answer (consistent with other knowledge pages)
        const introTextValue = introData.answer_p || introData.answer || '';
        setIntroText(introTextValue);
        setInitialIntroText(introTextValue);



        // Set photo if exists
        if (introData.photo_url && introData.photo_id) {
          const photoData = {
            id: 0, // We don't need the actual ID here
            photo_id: introData.photo_id,
            photo_url: Array.isArray(introData.photo_url) && introData.photo_url.length > 0
              ? introData.photo_url[0]
              : null,
            full_photo_urls: introData.photo_url
          };

          setSelectedIntroPhoto(photoData);
          setInitialSelectedIntroPhoto({...photoData});
        }
      }



      // Reset change tracking
      setHasIntroChanges(false);

    } catch (error) {
      console.error('Error loading intro data:', error);
    } finally {
      // Clear loading state
      setIsLoadingIntroData(false)
    }
  };

  // Load initial data - only after client info is available
  useEffect(() => {
    if (clientInfo?.client_id) {
      const initializeData = async () => {
        try {
          await Promise.all([
            loadIntroData(),
            // Fetch all photos on page load
          ]);
        } catch (error) {
          console.error('Error initializing data:', error);
        }
      };

      initializeData();
    } else if (clientInfo !== undefined) {
      // If clientInfo is defined but doesn't have client_id, stop loading
      setIsLoadingIntroData(false)
    }
  }, [clientInfo?.client_id]); // Only run when client info is loaded









  // Memoize the handleSaveEdit function
  const handleSaveEdit = useCallback(() => {
    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      if (!editingItem) {
        return;
      }

      // Save the changes
      handleTextChange(editingItem.value);

      // Close modal after state updates
      setEditingItem(null);
    });
  }, [editingItem]);



  // Effect to auto-focus textarea when editing popup appears
  useEffect(() => {
    // We need to track if this is the initial appearance of the popup
    if (editingItem && textareaRef.current && !hasFocusedInput) {
      // Check if device is desktop (not mobile/tablet) for auto-focus behavior
      const isDesktop = window.innerWidth >= 1024 && !('ontouchstart' in window)

      if (isDesktop) {
        // Use requestAnimationFrame for smoother focusing
        requestAnimationFrame(() => {
          if (textareaRef.current) {
            // Focus the textarea
            textareaRef.current.focus();

            // Place cursor at the end of the text
            const length = textareaRef.current.value.length;
            textareaRef.current.setSelectionRange(length, length);
            setHasFocusedInput(true);
          }
        });
      }
      // On mobile/tablet: let user manually tap to position cursor and handle keyboard
      // Don't set hasFocusedInput to true yet - wait for user's first click
    }
  }, [editingItem, hasFocusedInput]);

  // Reset focus tracking when modal closes
  useEffect(() => {
    if (!editingItem) {
      setHasFocusedInput(false);
    }
  }, [editingItem]);



  // Update change tracking whenever relevant data changes
  useEffect(() => {
    if (isIntroEditing) {
      setHasIntroChanges(checkIntroChanges());
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [introText, selectedIntroPhoto, isIntroEditing, initialIntroText, initialSelectedIntroPhoto]);



  // Add debug logging for content validation
  useEffect(() => {
    if (isIntroEditing) {
      const hasContent = hasValidIntroContent();
      const hasChanges = hasIntroChanges;
    }
  }, [introText, hasIntroChanges, isIntroEditing]);



  // Disable page scroll when gallery is open and add keyboard navigation
  useEffect(() => {
    if (imageGallery) {
      document.body.style.overflow = 'hidden';

      // Add keyboard navigation for photo gallery
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          showPreviousImage();
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          showNextImage();
        } else if (event.key === 'Escape') {
          event.preventDefault();
          setImageGallery(null);
        }
      };

      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.body.style.overflow = '';
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [imageGallery, showPreviousImage, showNextImage]);

  // Disable page scroll when any popup/modal is open
  useEffect(() => {
    const hasAnyPopup = editingItem || showSaveConfirmation || showCancelConfirmation ||
                       saveStatus === 'loading' || saveStatus === 'success' || saveStatus === 'error' ||
                       isLoadingIntroData || imageGallery;

    if (hasAnyPopup) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [editingItem, showSaveConfirmation, showCancelConfirmation, saveStatus,
      isLoadingIntroData, imageGallery]);

  return (
    <div className={themeConfig.pageBackground}>
      {/* Background effects */}
      {themeConfig.backgroundEffects}

      {/* Theme-aware Header */}
      <header className="relative">
        <div className="container mx-auto px-3 py-3">
          <div
            className={`relative ${themeConfig.headerBackground} rounded-2xl px-4 py-3 border ${themeConfig.headerBorder} ${themeConfig.headerHoverBorder} transition-all duration-300 overflow-hidden`}
          >


            {/* Content */}
            <div className="relative z-10 flex justify-center items-center">
              <LinkButton 
                href="/dashboard" 
                variant="ghost" 
                className="p-0 hover:bg-transparent active:scale-95"
              >
                <img
                  src={themeConfig.logo}
                  alt="Chhlat Bot"
                  className="h-8 w-auto transition-transform duration-300 hover:scale-105"
                />
              </LinkButton>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Content header with title and back button */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <LinkButton
                href="/dashboard"
                variant="secondary"
                size="sm"
                className={`inline-flex items-center text-sm ${theme === 'light' ? 'bg-white border border-gray-300 text-zinc-900 hover:bg-gray-50' : ''}`}
                leftIcon={
                  <svg
                    className="w-4 h-4 -ml-0.5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
              >
                {t('back')}
              </LinkButton>

              <h1 className={`text-2xl md:text-3xl font-extrabold font-title ${themeConfig.textPrimary}`}>
                {t('ai_brain')}
              </h1>

              {/* Empty div for balanced spacing */}
              <div className="w-10"></div>
            </div>
          </div>

          {/* Top Section */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-6">
            {/* Left Card: Infographics */}
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-3 sm:p-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Statistics</h2> */}

              {/* Modern Circle Stats Grid */}
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                {/* Business Insight Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow1">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill={themeConfig.statCircleBackground}
                        stroke={themeConfig.statCircleBorder}
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke={themeConfig.statCircleTrack}
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray={`${Math.min((totalFaqs / (totalFaqsLimit || 1)) * 251.2, 251.2)} 251.2`}
                        strokeDashoffset="0"
                        transform="rotate(-90 50 50)"
                        filter="url(#glow1)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full ${themeConfig.skeletonElement} border ${themeConfig.inputBorder} flex items-center justify-center`} style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaBrain className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('brain')}</p>

                  {/* Count */}
                  <p className={`${themeConfig.textPrimary} text-xs sm:text-base font-body`}>
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.textPrimary}`}></span>
                      </span>
                      : <>{totalFaqs} <span className={themeConfig.textMuted}>/ {totalFaqsLimit || 0}</span></>
                    }
                  </p>
                </div>

                {/* Photo Gallery Stat - Circle Design */}
                <div className="flex flex-col items-center justify-center text-center">
                  <div className="relative h-16 w-16 sm:h-24 sm:w-24 mb-2 sm:mb-3">
                    {/* Progress Circle - SVG implementation - Stroke only */}
                    <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                      {/* Outer glow filter */}
                      <defs>
                        <filter id="glow2">
                          <feGaussianBlur stdDeviation="2.5" result="blur" />
                          <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                      </defs>

                      {/* Background circle */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill={themeConfig.statCircleBackground}
                        stroke={themeConfig.statCircleBorder}
                        strokeWidth="1"
                      />

                      {/* Base track */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke={themeConfig.statCircleTrack}
                        strokeWidth="6"
                      />

                      {/* Progress track with glow */}
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="transparent"
                        stroke="rgba(134, 107, 255, 0.9)"
                        strokeWidth="6"
                        strokeDasharray="251.2"
                        strokeDashoffset={251.2 - Math.min((photoCount / (photoLimit || 1)) * 251.2, 251.2)}
                        transform="rotate(-90 50 50)"
                        filter="url(#glow2)"
                      />
                    </svg>

                    {/* Icon in Center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={`w-10 h-10 sm:w-14 sm:h-14 rounded-full ${themeConfig.skeletonElement} border ${themeConfig.inputBorder} flex items-center justify-center`} style={{
                        boxShadow: 'inset 0 0 10px rgba(255, 255, 255, 0.2)'
                      }}>
                        <FaImage className="w-5 h-5 sm:w-7 sm:h-7 text-jade-purple/75" />
                      </div>
                    </div>
                  </div>

                  {/* Label */}
                  <p className={`${themeConfig.textSecondary} text-[10px] sm:text-xs font-body mb-0 sm:mb-1`}>{t('photo_gallery')}</p>

                  {/* Count */}
                  <p className={`${themeConfig.textPrimary} text-xs sm:text-base font-body`}>
                    {isLoadingCount ?
                      <span className="flex justify-center">
                        <span className={`w-3 h-3 sm:w-4 sm:h-4 border-2 border-current border-t-transparent rounded-full animate-spin ${themeConfig.textPrimary}`}></span>
                      </span>
                      : <>{photoCount} <span className={themeConfig.textMuted}>/ {photoLimit || 0}</span></>
                    }
                  </p>
                </div>
              </div>
              </div>
            </div>

            {/* Right Card: Action Buttons */}
            <div
              className={`relative ${themeConfig.cardBackground} rounded-2xl p-3 sm:p-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}>

              <div className="relative z-10">
              {/* <h2 className="text-base sm:text-xl font-bold mb-2 sm:mb-4 font-title text-center">Knowledge Management</h2> */}

              {/* Buttons Grid */}
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <LinkButton
                  href="/dashboard/knowledge"
                  variant="secondary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={false}
                >
                  {t('business_insight')}
                </LinkButton>

                <LinkButton
                  href="/dashboard/knowledge/photo"
                  variant="secondary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={false}
                >
                  {t('photo_gallery')}
                </LinkButton>

                <LinkButton
                  href="/dashboard/knowledge/intro"
                  variant="primary"
                  size="md"
                  className="text-xs sm:text-base py-2 sm:py-3 w-full text-center"
                  isActive={true}
                >
                  {t('intros_outros')}
                </LinkButton>

              </div>
              </div>
            </div>
          </div>

          {/* Intro Section */}
          <div
            className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 mb-6 border ${themeConfig.cardBorder} ${themeConfig.cardHoverBorder} transition-all duration-300 group overflow-hidden`}
          >
            <div className="relative z-10">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center">
                <FaComments className={`${themeConfig.textPrimary} mr-3 h-5 w-5`} />
                <h2 className={`text-xl font-bold font-title ${themeConfig.textPrimary}`}>{t('intro_message')}</h2>
              </div>
              <div className="flex space-x-2">
                {isIntroEditing ? (
                  <>
                    <Button
                      onClick={() => handleCancelEdit()}
                      variant="secondary"
                      size="sm"
                      className="text-xs sm:text-base"
                    >
                      {t('cancel')}
                    </Button>
                    <div className="relative">
                      <Button
                        onClick={() => showSaveConfirmationPopup()}
                        variant="primary"
                        size="sm"
                        className="text-xs sm:text-base"
                        disabled={isIntroSaving || !hasIntroChanges || !hasValidIntroContent()}
                        title={!hasValidIntroContent() ? "Add text or audio before saving" : (!hasIntroChanges ? "No changes to save" : "Save changes")}
                        isLoading={isIntroSaving}
                        loadingText={t('saving')}
                      >
                        {t('save')}
                      </Button>
                      {isIntroEditing && !hasValidIntroContent() && (
                        <div className="absolute -bottom-6 right-0 text-xs text-red-400 whitespace-nowrap">
                          Add text or audio
                        </div>
                      )}
                    </div>
                  </> 
                ) : (
                  <Button
                    onClick={() => handleEdit()}
                    variant="secondary"
                    size="sm"
                    className="text-xs sm:text-base"
                  >
                    {t('edit')}
                  </Button>
                )}
              </div>
            </div>
            <p className={`${themeConfig.textSecondary} text-sm mb-4 md:mb-6 font-body`}>
              {t('intro_description')}
            </p>

            {/* Loading Animation */}
            {isLoadingIntroData ? (
              <>
                {/* Loading header */}
                <div className="py-8 flex justify-center items-center">
                  <div className="flex flex-col items-center space-y-4">
                    {/* Main loading spinner */}
                    <div className="relative">
                      <div className="w-12 h-12 border-4 border-jade-purple/20 border-t-jade-purple rounded-full animate-spin"></div>
                      <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-jade-purple/40 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                    </div>

                    {/* Loading text with animation */}
                    <div className="text-center">
                      <p className="text-white text-lg font-medium mb-1">{t('loading')}</p>
                    </div>

                    {/* Progress indicator */}
                    <div className="w-48 h-1 bg-zinc-700 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-jade-purple to-jade-purple-dark rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Skeleton loading for intro form */}
                <div className="space-y-4">
                  {/* Photo search skeleton */}
                  <div className="h-10 bg-zinc-700/30 rounded-lg animate-pulse"></div>

                  {/* Answer input skeleton */}
                  <div className="h-12 bg-zinc-700/30 rounded-lg animate-pulse" style={{ animationDelay: '0.1s' }}></div>
                </div>
              </>
            ) : (
              <>
                {/* Photo Search Bar */}
                <div className="mb-4 relative">
              <div className="relative">
                <input
                  type="text"
                  value={introSearchQuery}
                  onChange={handleIntroPhotoSearch}
                  onFocus={() => {
                    if (introSearchQuery.trim() && introSearchResults.length > 0) {
                      setShowIntroResults(true);
                    }
                  }}
                  placeholder={t('search_photo_placeholder')}
                  disabled={!isIntroEditing}
                  className={`w-full px-3 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none ${isIntroEditing ? 'focus:border-gray-500' : 'cursor-not-allowed opacity-70'} hover:border-gray-500`}
                  style={{
                    fontSize: '16px' // Prevent auto-zoom on mobile
                  }}
                  ref={introSearchInputRef}
                  autoComplete="off" // Prevent browser autocomplete from interfering
                  spellCheck="false" // Disable spell checking
                />
                {isSearchingIntro && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Search Results Dropdown */}
              {showIntroResults && (
                <div
                  className={`absolute z-50 w-full mt-2 rounded-xl shadow-2xl max-h-60 overflow-y-auto backdrop-blur-lg ${theme === 'light' ? 'bg-white border border-gray-300' : 'bg-white/20'}`}
                  ref={introSearchResultsRef}
                >
                  {introSearchResults.length > 0 ? (
                    introSearchResults.map(photo => (
                      <div
                        key={photo.id}
                        className={`flex items-center gap-3 p-3 cursor-pointer border-b transition-colors duration-200 last:border-0 ${theme === 'light' ? 'hover:bg-gray-100 border-gray-200' : 'hover:bg-white/10 border-white/5'}`}
                        onClick={() => handleSelectIntroPhoto(photo)}
                        style={{
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {/* Photo Thumbnail - Optimized */}
                        <PhotoThumbnail
                          photo={photo}
                          className="w-10 h-10"
                        />
                        {/* Photo ID */}
                        <div className="flex-1 truncate">
                          <p className={`${themeConfig.textPrimary} truncate`}>{photo.photo_id}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className={`p-3 ${themeConfig.textMuted} text-center`}>
                      No photos found
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Photo Display or Loading Animation */}
            {isIntroPhotoLoading ? (
              <div className={`mb-4 p-4 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-xl flex items-center justify-center h-16`}>
                <div className="flex items-center space-x-3">
                  <div className="w-5 h-5 border-2 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                  <span className={`${themeConfig.textSecondary} text-sm`}>{t('loading')}</span>
                </div>
              </div>
            ) : selectedIntroPhoto && (
              <div className={`mb-4 p-3 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} hover:border-gray-500 rounded-xl flex items-center justify-between animate-fadeIn`}>
                <div className="flex items-center gap-3">
                  {/* Photo Thumbnail - Optimized */}
                  <PhotoThumbnail
                    photo={{
                      photo_url: selectedIntroPhoto.full_photo_urls || (selectedIntroPhoto.photo_url ? [selectedIntroPhoto.photo_url] : null),
                      photo_id: selectedIntroPhoto.photo_id
                    }}
                    className={`w-10 h-10 border ${theme === 'light' ? 'border-gray-300' : 'border-white/20'}`}
                    onClick={() => handleViewImage(selectedIntroPhoto.full_photo_urls || (selectedIntroPhoto.photo_url ? [selectedIntroPhoto.photo_url] : null))}
                  />
                  {/* Photo ID */}
                  <div>
                    <p className={themeConfig.textPrimary}>{selectedIntroPhoto.photo_id}</p>
                  </div>
                </div>
                {/* Remove Button */}
                <button
                  disabled={!isIntroEditing}
                  onClick={(e) => isIntroEditing && handleClearSelectedPhoto(e)}
                  className={`p-1 rounded-full ${theme === 'light' ? 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 border border-gray-300' : 'bg-white/5 hover:bg-white/10 text-zinc-300 hover:text-white border border-white/10'} transition-colors duration-200`}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}

            {/* Answer Input */}
            <div className="mb-4">
              <div
                className={`px-2 md:px-4 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none ${isIntroEditing ? 'hover:border-gray-500 cursor-pointer' : 'opacity-70'} hover:border-gray-500 flex items-center min-h-[42px] relative`}

                onClick={() => {
                  if (!isIntroEditing) {
                    return;
                  }
                  setEditingItem({
                    value: introText
                  });
                }}
              >
                {/* Regular input display */}
                <span className="truncate pr-10">
                  {introText || <span className="text-zinc-500">{t('enter_welcome_message')}</span>}
                </span>







              </div>
            </div>
              </>
            )}
            </div>
          </div>






          {/* Edit modal*/}
          {editingItem && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-[53]`}
            >
              <div
                ref={modalRef}
                className={`relative rounded-2xl p-6 w-full max-w-lg mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple-dark/[0.3] border-white/20'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                <div className="relative z-10" data-modal-content>
                  {/* Close button (X) */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className={`absolute top-0 right-0 p-1 rounded-full ${theme === 'light' ? 'bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 border border-gray-300' : 'bg-black/40 hover:bg-jade-purple/30 text-white/60 hover:text-white border border-white/20'}`}
                    onClick={() => setEditingItem(null)}
                    aria-label="Close"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </Button>

                  <h3 className="text-xl text-white/80 font-bold mb-4 font-title text-center">
                    {t('edit_intro_message')}
                  </h3>
                  <textarea
                    ref={textareaRef}
                    value={editingItem.value}
                    onChange={(e) => {
                      // Only update if value actually changed
                      if (e.target.value !== editingItem.value) {
                        setEditingItem(prev => prev ? {...prev, value: e.target.value} : null);
                      }
                    }}
                    onClick={() => {
                      // On mobile: only position cursor at end on FIRST click (initial focus)
                      // After that, allow free cursor movement
                      if (!hasFocusedInput && textareaRef.current) {
                        const length = textareaRef.current.value.length;
                        textareaRef.current.setSelectionRange(length, length);
                        setHasFocusedInput(true);
                      }
                      // Subsequent clicks: let user position cursor freely (default browser behavior)
                    }}
                    className={`w-full px-4 py-2 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} border-2 ${themeConfig.inputBorder} rounded-lg ${themeConfig.inputText} focus:outline-none min-h-[150px] mb-4`}
                    placeholder={t('enter_welcome_message')}
                  />
                  <Button
                    onClick={handleSaveEdit}
                    variant="primary"
                    size="md"
                    className="w-full bg-jade-purple-dark text-white hover:bg-jade-purple hover:shadow-md transition-all duration-200 border border-gray-300"
                  >
                    {t('done')}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Save Confirmation Modal */}
          {showSaveConfirmation && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-jade-purple/[0.8] border-jade-purple/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('save_intro_message')}
                  </h3>

                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('save_intro_confirmation')}
                  </p>

                  <div className="flex space-x-3">
                    <Button
                      onClick={() => setShowSaveConfirmation(false)}
                      variant="secondary"
                      size="md"
                      className="flex-1"
                      disabled={saveStatus !== 'idle'}
                    >
                      {t('cancel')}
                    </Button>
                    <Button
                      onClick={() => {
                        handleSave();
                      }}
                      variant="primary"
                      size="md"
                      className="flex-1"
                      disabled={saveStatus !== 'idle'}
                    >
                      {t('save')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Cancel Confirmation Modal */}
          {showCancelConfirmation && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('discard_changes')}
                  </h3>

                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('unsaved_intro_changes')}
                  </p>

                  <div className="flex justify-between w-full space-x-4">
                    <Button
                      onClick={() => setShowCancelConfirmation(false)}
                      variant="secondary"
                      size="md"
                      className="flex-1"
                    >
                      {t('keep_editing')}
                    </Button>
                    <Button
                      onClick={confirmCancel}
                      variant="danger"
                      size="md"
                      className="flex-1"
                    >
                      {t('discard')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}


            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('delete_audio')}
                  </h3>

                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('delete_intro_audio_confirmation')}
                  </p>

                  <div className="flex justify-between w-full space-x-4">
                    <Button
                      onClick={() => setShowDeleteAudioConfirmation(false)}
                      variant="secondary"
                      size="md"
                      className="flex-1"
                    >
                      {t('cancel')}
                    </Button>
                    <Button
                      onClick={deleteAudio}
                      variant="danger"
                      size="md"
                      className="flex-1"
                    >
                      {t('delete')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Audio Error Popup - similar to the one in Knowledge page */}
          {showAudioErrorPopup && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.5] border-red-500/50'}`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                  {/* Close button */}
                  <Button
                    onClick={handleCloseAudioErrorPopup}
                    variant="ghost"
                    size="icon"
                    className="absolute top-0 right-0 p-1 rounded-full bg-black/40 hover:bg-jade-purple/30 text-white/60 hover:text-white border border-gray-300"
                    aria-label="Close"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </Button>

                  {/* Error icon */}
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-900/[0.8] flex items-center justify-center">
                    <FaExclamationTriangle className="w-8 h-8 text-red-400" />
                  </div>

                  {/* Error title */}
                  <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.textPrimary}`}>
                    {t('audio_recording_error')}
                  </h3>

                  {/* Error message */}
                  <p className={`${themeConfig.textSecondary} mb-6 text-center`}>
                    {t('audio_recording_too_long')}
                  </p>

                  <Button
                    onClick={handleCloseAudioErrorPopup}
                    variant="danger"
                    size="md"
                    className="w-full bg-red-800/90 hover:bg-red-700/90 border border-gray-300"
                  >
                    {t('close')}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Save Status Overlay */}
          {saveStatus !== 'idle' && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
              onClick={() => saveStatus !== 'loading' && setSaveStatus('idle')}
            >
              <div
                className={`relative ${
                  saveStatus === 'loading' 
                    ? 'bg-jade-purple-dark/[0.6] border-white/20' 
                    : saveStatus === 'success'
                      ? 'bg-green-900/[0.8] border-green-500/50'
                      : 'bg-red-900/[0.8] border-red-500/50'
                } backdrop-blur-xl rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden`}
                onClick={(e) => e.stopPropagation()}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${
                  saveStatus === 'loading'
                    ? 'from-jade-purple/10'
                    : saveStatus === 'success'
                      ? 'from-green-500/10'
                      : 'from-red-500/10'
                } to-transparent opacity-50 rounded-2xl`}></div>
                <div className="relative z-10">
                  {saveStatus === 'loading' ? (
                    <div className="flex flex-col items-center text-center">
                      <div className="w-12 h-12 mb-4 border-3 border-jade-purple border-t-transparent rounded-full animate-spin"></div>
                      <p className="text-lg font-semibold mb-3 text-white">{t('saving_intro_message')}</p>
                      <div className="w-full bg-black/30 rounded-full h-2 mb-1">
                        <div
                          className="h-full bg-jade-purple rounded-full"
                          style={{ width: `${updateProgress}%` }}
                        ></div>
                      </div>
                      <p className={`text-sm ${themeConfig.textSecondary}`}>{updateProgress}% {t('complete')}</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                        saveStatus === 'success' ? 'bg-green-900/[0.8]' : 'bg-red-900/[0.8]'
                      }`}>
                        {saveStatus === 'success' ? (
                          <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        ) : (
                          <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        )}
                      </div>
                      <p className={`text-lg font-semibold mb-1 ${themeConfig.textPrimary}`}>
                        {saveStatus === 'success' ? t('success') : t('error')}
                      </p>
                      <p className={`${themeConfig.textSecondary} text-center text-sm mb-4`}>{updateMessage}</p>
                      <Button
                        onClick={() => setSaveStatus('idle')}
                        variant={saveStatus === 'success' ? 'success' : 'danger'}
                        size="sm"
                        className="mt-4"
                      >
                        {t('close')}
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Image Gallery Modal */}
          {imageGallery && (
            <div
              className={`fixed inset-0 ${theme === 'light' ? 'bg-black/30' : 'bg-black/50'} backdrop-blur-sm flex items-center justify-center z-50`}
            >
              <div
                className={`relative ${themeConfig.cardBackground} rounded-2xl p-6 w-full max-w-3xl mx-4 border ${themeConfig.cardBorder} overflow-hidden`}
                onClick={(e: React.MouseEvent) => e.stopPropagation()}
              >
                {theme === 'dark' && (
                  <div className="absolute inset-0 bg-gradient-to-br from-jade-purple/10 to-transparent opacity-50 rounded-2xl"></div>
                )}
                <div className="relative z-10">
                {/* Close button in the top-right corner */}
                <button
                  onClick={() => setImageGallery(null)}
                  className={`absolute top-0 right-0 p-1.5 border ${themeConfig.inputBorder} rounded-full ${theme === 'light' ? 'bg-white' : 'bg-black/30'} hover:bg-jade-purple ${themeConfig.textMuted} hover:text-white transition-colors z-20`}
                  aria-label="Close"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>

                <div className="flex flex-col">
                  {/* Image counter */}
                  <div className="text-center mb-2 text-sm text-white/60">
                    {imageGallery.currentIndex + 1} / {imageGallery.urls.length}
                  </div>

                  {/* Main image container with touch events */}
                  <div
                    className="w-full flex items-center justify-center h-[60vh]"
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                    aria-live="polite"
                    role="region"
                    aria-label={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                  >
                    {imageGallery.urls.length === 0 ? (
                      <div className="flex flex-col items-center justify-center text-white/50">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p>No images available</p>
                      </div>
                    ) : (
                      <div className="relative">
                        <img
                          src={optimizeGalleryImage(imageGallery.urls[imageGallery.currentIndex])}
                          alt={`Image ${imageGallery.currentIndex + 1} of ${imageGallery.urls.length}`}
                          className="rounded-lg max-w-full max-h-full object-contain"
                          loading="eager"
                          decoding="async"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/placeholder-image.svg';
                          }}
                        />
                      </div>
                    )}

                {/* Navigation buttons - only show if more than one image */}
                {imageGallery.urls.length > 1 && (
                  <>
                    {/* Previous button */}
                    <button
                      onClick={showPreviousImage}
                      className="absolute left-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Previous image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>

                    {/* Next button */}
                    <button
                      onClick={showNextImage}
                      className="absolute right-2 p-2 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                      aria-label="Next image"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}
                  </div>

                  {/* Thumbnail strip - only show if more than one image */}
                  {imageGallery.urls.length > 1 && (
                    <div className="flex justify-center space-x-2 mt-4 overflow-x-auto py-2">
                      {imageGallery.urls.map((url, index) => (
                        <PhotoThumbnail
                          key={index}
                          photo={{
                            photo_url: [url],
                            photo_id: `thumbnail-${index + 1}`
                          }}
                          className={`w-16 h-16 rounded-lg border transition-all duration-200 ease-in-out ${
                            index === imageGallery.currentIndex ? 'border-gray-300 scale-105 opacity-100' : 'border-gray-300 opacity-70 hover:opacity-90 hover:border-gray-300'
                          }`}
                          onClick={() => {
                            setImageGallery({
                              ...imageGallery,
                              currentIndex: index
                            });
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
      <Footer />
    </div>
  );
}
